<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Location Service Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    .test-section {
      margin: 20px 0;
      padding: 15px;
      border: 1px solid #ddd;
      border-radius: 5px;
    }
    .success {
      background-color: #d4edda;
      border-color: #c3e6cb;
    }
    .error {
      background-color: #f8d7da;
      border-color: #f5c6cb;
    }
    .loading {
      background-color: #fff3cd;
      border-color: #ffeaa7;
    }
    button {
      background-color: #007bff;
      color: white;
      border: none;
      padding: 10px 15px;
      border-radius: 3px;
      cursor: pointer;
      margin: 5px;
    }
    button:hover {
      background-color: #0056b3;
    }
    select {
      padding: 5px;
      margin: 5px;
      min-width: 150px;
    }
    pre {
      background-color: #f8f9fa;
      padding: 10px;
      border-radius: 3px;
      overflow-x: auto;
    }
  </style>
</head>
<body>
  <h1>Tavoos Location Service Test</h1>

  <div class="test-section">
    <h2>API Connectivity Test</h2>
    <button onclick="testContinentsAPI()">Test Continents API</button>
    <div id="continents-result"></div>
  </div>

  <div class="test-section">
    <h2>Location Selection Test</h2>
    <div>
      <select id="test-continents">
        <option value="">Select Continent</option>
      </select>
      <select id="test-countries">
        <option value="">Select Country</option>
      </select>
      <select id="test-cities">
        <option value="">Select City</option>
      </select>
    </div>
    <button onclick="loadContinents()">Load Continents</button>
    <div id="selection-result"></div>
  </div>

  <div class="test-section">
    <h2>Default Location Test</h2>
    <button onclick="testDefaultLocation()">Test Default Location (EU -> BE -> Gent)</button>
    <div id="default-location-result"></div>
  </div>

  <div class="test-section">
    <h2>Storage Test</h2>
    <button onclick="testStorage()">Test Chrome Storage</button>
    <div id="storage-result"></div>
  </div>

  <script>
    // Mock Chrome storage for testing outside of extension context
    if (typeof chrome === 'undefined') {
      window.chrome = {
        storage: {
          sync: {
            get: function(keys, callback) {
              const mockData = {};
              if (typeof keys === 'string') {
                mockData[keys] = localStorage.getItem(keys) ? JSON.parse(localStorage.getItem(keys)) : null;
              } else if (Array.isArray(keys)) {
                keys.forEach(key => {
                  mockData[key] = localStorage.getItem(key) ? JSON.parse(localStorage.getItem(key)) : null;
                });
              }
              callback(mockData);
            },
            set: function(data, callback) {
              Object.keys(data).forEach(key => {
                localStorage.setItem(key, JSON.stringify(data[key]));
              });
              if (callback) callback();
            },
            remove: function(keys, callback) {
              if (typeof keys === 'string') {
                localStorage.removeItem(keys);
              } else if (Array.isArray(keys)) {
                keys.forEach(key => localStorage.removeItem(key));
              }
              if (callback) callback();
            }
          }
        }
      };
    }

    // Simple LocationService for testing
    class LocationService {
      constructor() {
        this.baseUrl = 'https://behnegar.app/api/v1/tavoos/pray-times-v2';
        this.storageKeys = {
          continents: 'tavoos_continents',
          countries: 'tavoos_countries',
          cities: 'tavoos_cities',
          selectedContinent: 'tavoos_selected_continent',
          selectedCountry: 'tavoos_selected_country',
          selectedCity: 'tavoos_selected_city'
        };
        this.defaults = {
          continent: { code: 'EU', name: 'Europe' },
          country: {
            europe: { code: 'BE', name: 'Belgium' },
            asia: { code: 'IR', name: 'Iran' }
          },
          city: { name: 'Gent' }
        };
      }

      async getContinents() {
        try {
          const cached = await this.getFromStorage(this.storageKeys.continents);
          if (cached && cached.length > 0) {
            return cached;
          }

          const response = await fetch(`${this.baseUrl}/continents`);
          if (!response.ok) {
            throw new Error(`Failed to fetch continents: ${response.status}`);
          }

          const continents = await response.json();
          try {
            await this.saveToStorage(this.storageKeys.continents, continents);
          } catch (storageError) {
            console.warn('Could not cache continents:', storageError);
          }
          return continents;
        } catch (error) {
          console.error('Error fetching continents:', error);
          return [];
        }
      }

      async getCountries(continentId) {
        try {
          const response = await fetch(`${this.baseUrl}/continents/${continentId}/countries`);
          if (!response.ok) {
            throw new Error(`Failed to fetch countries: ${response.status}`);
          }

          const countries = await response.json();
          return countries;
        } catch (error) {
          console.error('Error fetching countries:', error);
          return [];
        }
      }

      async getCities(countryId) {
        try {
          const response = await fetch(`${this.baseUrl}/countries/${countryId}/cities`);
          if (!response.ok) {
            throw new Error(`Failed to fetch cities: ${response.status}`);
          }

          const cities = await response.json();
          return cities;
        } catch (error) {
          console.error('Error fetching cities:', error);
          return [];
        }
      }

      async getFromStorage(key) {
        return new Promise((resolve) => {
          chrome.storage.sync.get([key], (result) => {
            resolve(result[key] || null);
          });
        });
      }

      async saveToStorage(key, value) {
        return new Promise((resolve) => {
          chrome.storage.sync.set({ [key]: value }, () => {
            resolve();
          });
        });
      }
    }

    const locationService = new LocationService();
    let continents = [];
    let countries = [];
    let cities = [];

    async function testContinentsAPI() {
      const resultDiv = document.getElementById('continents-result');
      resultDiv.className = 'loading';
      resultDiv.innerHTML = 'Testing continents API...';

      try {
        const continents = await locationService.getContinents();
        resultDiv.className = 'success';
        resultDiv.innerHTML = `
                    <h3>Success!</h3>
                    <p>Found ${continents.length} continents</p>
                    <pre>${JSON.stringify(continents.slice(0, 3), null, 2)}</pre>
                    ${continents.length > 3 ? '<p>... and more</p>' : ''}
                `;
      } catch (error) {
        resultDiv.className = 'error';
        resultDiv.innerHTML = `<h3>Error!</h3><p>${error.message}</p>`;
      }
    }

    async function loadContinents() {
      try {
        continents = await locationService.getContinents();
        const select = document.getElementById('test-continents');
        select.innerHTML = '<option value="">Select Continent</option>';

        continents.forEach(continent => {
          const option = document.createElement('option');
          option.value = continent.id;
          option.textContent = continent.name;
          select.appendChild(option);
        });

        document.getElementById('selection-result').innerHTML =
        `<p class="success">Loaded ${continents.length} continents</p>`;
      } catch (error) {
        document.getElementById('selection-result').innerHTML =
        `<p class="error">Error: ${error.message}</p>`;
      }
    }

    async function testDefaultLocation() {
      const resultDiv = document.getElementById('default-location-result');
      resultDiv.className = 'loading';
      resultDiv.innerHTML = 'Testing default location initialization...';

      try {
        // Test the default location logic
        const continents = await locationService.getContinents();
        const europeContinent = continents.find(c => c.code === 'EU');

        if (!europeContinent) {
          throw new Error('Europe continent not found');
        }

        const countries = await locationService.getCountries(europeContinent.id);
        const belgiumCountry = countries.find(c => c.code === 'BE');

        if (!belgiumCountry) {
          throw new Error('Belgium not found in Europe');
        }

        const cities = await locationService.getCities(belgiumCountry.id);
        const gentCity = cities.find(c => c.name === 'Gent');

        resultDiv.className = 'success';
        resultDiv.innerHTML = `
                    <h3>Default Location Test Success!</h3>
                    <p><strong>Continent:</strong> ${europeContinent.name} (${europeContinent.code})</p>
                    <p><strong>Country:</strong> ${belgiumCountry.name} (${belgiumCountry.code})</p>
                    <p><strong>City:</strong> ${gentCity ? gentCity.name : 'Gent not found, would use first city'}</p>
                    <p><strong>Total cities in Belgium:</strong> ${cities.length}</p>
                `;
      } catch (error) {
        resultDiv.className = 'error';
        resultDiv.innerHTML = `<h3>Error!</h3><p>${error.message}</p>`;
      }
    }

    async function testStorage() {
      const resultDiv = document.getElementById('storage-result');
      resultDiv.className = 'loading';
      resultDiv.innerHTML = 'Testing Chrome storage...';

      try {
        // Test storage operations
        const testData = { test: 'value', timestamp: Date.now() };
        await locationService.saveToStorage('test_key', testData);

        const retrieved = await locationService.getFromStorage('test_key');

        if (JSON.stringify(retrieved) === JSON.stringify(testData)) {
          resultDiv.className = 'success';
          resultDiv.innerHTML = `
                        <h3>Storage Test Success!</h3>
                        <p>Successfully saved and retrieved data</p>
                        <pre>${JSON.stringify(retrieved, null, 2)}</pre>
                    `;
        } else {
          throw new Error('Retrieved data does not match saved data');
        }
      } catch (error) {
        resultDiv.className = 'error';
        resultDiv.innerHTML = `<h3>Storage Error!</h3><p>${error.message}</p>`;
      }
    }

    // Set up event listeners for dropdowns
    document.getElementById('test-continents').addEventListener('change', async (e) => {
      const continentId = e.target.value;
      const countriesSelect = document.getElementById('test-countries');
      const citiesSelect = document.getElementById('test-cities');

      countriesSelect.innerHTML = '<option value="">Select Country</option>';
      citiesSelect.innerHTML = '<option value="">Select City</option>';

      if (continentId) {
        try {
          countries = await locationService.getCountries(continentId);
          countries.forEach(country => {
            const option = document.createElement('option');
            option.value = country.id;
            option.textContent = country.name;
            countriesSelect.appendChild(option);
          });
        } catch (error) {
          console.error('Error loading countries:', error);
        }
      }
    });

    document.getElementById('test-countries').addEventListener('change', async (e) => {
      const countryId = e.target.value;
      const citiesSelect = document.getElementById('test-cities');

      citiesSelect.innerHTML = '<option value="">Select City</option>';

      if (countryId) {
        try {
          cities = await locationService.getCities(countryId);
          cities.forEach(city => {
            const option = document.createElement('option');
            option.value = city.id;
            option.textContent = city.name;
            citiesSelect.appendChild(option);
          });
        } catch (error) {
          console.error('Error loading cities:', error);
        }
      }
    });
  </script>
</body>
</html>
