{"name": "tavoos-chrome-extension", "version": "*******", "description": "Islamic companion Chrome extension with Svelte new tab", "main": "background.js", "scripts": {"dev": "vite", "build": "vite build", "build:newtab": "vite build && npm run copy-assets", "copy-assets": "cp -r fonts dist/ && cp android-chrome-192x192.png dist/ && cp manifest.json dist/ && cp background.js dist/ && cp dist/src/newtab/index.html dist/newtab.html && sed -i '' 's|\\.\\.\\./\\.\\.\\./newtab|\\./newtab|g' dist/newtab.html", "preview": "vite preview"}, "keywords": ["chrome-extension", "islamic", "prayer-times", "qibla", "quran", "svelte"], "author": "Tavoos", "license": "ISC", "type": "module", "bugs": {"url": "https://gitlab.com/tavoos/tavoos-chrome-extension/issues"}, "homepage": "https://gitlab.com/tavoos/tavoos-chrome-extension#readme", "devDependencies": {"@sveltejs/vite-plugin-svelte": "^6.0.0", "svelte": "^5.35.6", "vite": "^7.0.4"}}