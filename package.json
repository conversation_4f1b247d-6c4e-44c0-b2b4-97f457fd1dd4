{"name": "tavoos-chrome-extension", "version": "1.0.0", "description": "", "main": "background.js", "scripts": {"dev": "vite", "build": "vite build", "build:popup": "vite build && npm run copy-assets && npm run fix-paths", "copy-assets": "cp -r qibla/images dist/ && cp android-chrome-192x192.png dist/ && cp manifest.json dist/ && cp background.js dist/ && cp -r newtab dist/ && cp -r fonts dist/", "fix-paths": "cp dist/src/popup/index.html dist/popup.html && sed -i '' 's|../../popup|./popup|g' dist/popup.html", "preview": "vite preview"}, "repository": {"type": "git", "url": "git+ssh://**************/tavoos/tavoos-chrome-extension.git"}, "keywords": [], "author": "", "license": "ISC", "type": "module", "bugs": {"url": "https://gitlab.com/tavoos/tavoos-chrome-extension/issues"}, "homepage": "https://gitlab.com/tavoos/tavoos-chrome-extension#readme", "devDependencies": {"@sveltejs/vite-plugin-svelte": "^6.0.0", "svelte": "^5.35.6", "vite": "^7.0.4"}}