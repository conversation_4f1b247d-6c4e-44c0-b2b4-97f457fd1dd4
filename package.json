{"name": "tavoos-chrome-extension", "version": "1.0.0", "description": "", "main": "background.js", "scripts": {"dev": "vite", "build": "vite build", "build:popup": "vite build && npm run copy-assets", "copy-assets": "cp -r qibla/images dist/ && cp android-chrome-192x192.png dist/", "preview": "vite preview"}, "repository": {"type": "git", "url": "git+ssh://**************/tavoos/tavoos-chrome-extension.git"}, "keywords": [], "author": "", "license": "ISC", "type": "module", "bugs": {"url": "https://gitlab.com/tavoos/tavoos-chrome-extension/issues"}, "homepage": "https://gitlab.com/tavoos/tavoos-chrome-extension#readme", "devDependencies": {"@sveltejs/vite-plugin-svelte": "^6.0.0", "svelte": "^5.35.6", "vite": "^7.0.4"}}