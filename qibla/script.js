// DOM Elements
const continentsSelect = document.querySelector('#continents');
const countriesSelect = document.querySelector('#countries');
const citiesSelect = document.querySelector('#cities');
const compass = document.querySelector('#compass');
const degreesElement = document.querySelector('#degrees');
const minutesElement = document.querySelector('#minutes');
const cityBox = document.querySelector('#city-box');

// Global variables
let continents = [];
let countries = [];
let cities = [];
let currentContinent = null;
let currentCountry = null;
let currentCity = null;

// Initialize location data
async function initializeLocationData() {
  try {
    // Load continents
    continents = await locationService.getContinents();
    populateContinentsDropdown();

    // Try to load stored location first
    const storedLocation = await locationService.getStoredLocation();

    if (storedLocation.continent && storedLocation.country && storedLocation.city) {
      // Use stored location
      currentContinent = storedLocation.continent;
      currentCountry = storedLocation.country;
      currentCity = storedLocation.city;

      // Populate dropdowns with stored data
      await populateCountriesDropdown(currentContinent.id);
      await populateCitiesDropdown(currentCountry.id);

      // Set selected values
      continentsSelect.value = currentContinent.id;
      countriesSelect.value = currentCountry.id;
      citiesSelect.value = currentCity.id;

      // Update UI
      updateChosenCity(currentCity);
      showCompass();
    } else {
      // Initialize with default location
      const defaultLocation = await locationService.initializeDefaultLocation();
      if (defaultLocation) {
        currentContinent = defaultLocation.continent;
        currentCountry = defaultLocation.country;
        currentCity = defaultLocation.city;

        // Populate dropdowns
        await populateCountriesDropdown(currentContinent.id);
        await populateCitiesDropdown(currentCountry.id);

        // Set selected values
        continentsSelect.value = currentContinent.id;
        countriesSelect.value = currentCountry.id;
        citiesSelect.value = currentCity.id;

        // Update UI
        updateChosenCity(currentCity);
        showCompass();
      } else {
        hideCompass();
      }
    }
  } catch (error) {
    console.error('Error initializing location data:', error);
    hideCompass();
  }
}

// Populate continents dropdown
function populateContinentsDropdown() {
  continentsSelect.innerHTML = '<option value="">Continent</option>';

  continents.forEach(continent => {
    const option = document.createElement('option');
    option.value = continent.id;
    option.textContent = continent.name;
    continentsSelect.appendChild(option);
  });
}

// Populate countries dropdown
async function populateCountriesDropdown(continentId) {
  countriesSelect.innerHTML = '<option value="">Country</option>';
  citiesSelect.innerHTML = '<option value="">City</option>';

  if (!continentId) return;

  try {
    countries = await locationService.getCountries(continentId);

    countries.forEach(country => {
      const option = document.createElement('option');
      option.value = country.id;
      option.textContent = country.name;
      countriesSelect.appendChild(option);
    });
  } catch (error) {
    console.error('Error loading countries:', error);
  }
}

// Populate cities dropdown
async function populateCitiesDropdown(countryId) {
  citiesSelect.innerHTML = '<option value="">City</option>';

  if (!countryId) return;

  try {
    cities = await locationService.getCities(countryId);

    cities.forEach(city => {
      const option = document.createElement('option');
      option.value = city.id;
      option.textContent = city.name;
      citiesSelect.appendChild(option);
    });
  } catch (error) {
    console.error('Error loading cities:', error);
  }
}

// Calculate Qibla direction
function calculateQiblaDirection(latitude, longitude) {
  const toRadians = deg => deg * Math.PI / 180;
  const toDegrees = rad => rad * 180 / Math.PI;

  const startLat = toRadians(latitude);
  const startLng = toRadians(longitude);

  // Coordinates of Mecca
  const destLat = toRadians(21.3891);
  const destLng = toRadians(39.8579);

  const y = Math.sin(destLng - startLng) * Math.cos(destLat);
  const x = Math.cos(startLat) * Math.sin(destLat) - Math.sin(startLat) * Math.cos(destLat) * Math.cos(destLng - startLng);
  const brng = toDegrees(Math.atan2(y, x));

  return brng + 180;
}



function plotCityName(city) {
  const cityBox = document.querySelector('#city-box');
  const countryName = currentCountry ? currentCountry.name : 'Unknown';
  cityBox.innerText = `${city.name} ${countryName}`;
}



function plotOrientation({ latitude, longitude }) {
  const brng = calculateQiblaDirection(latitude, longitude);

  const hand = document.createElement('div');
  hand.classList.add('hand');
  hand.style.transform = `translate(100px, 100px) rotate(${brng + 90 - 11.5}deg)`;

  compass.innerHTML = '';
  compass.appendChild(hand);

  const degrees = Math.floor(brng);
  const minutes = Math.floor(60 * (brng - degrees));

  degreesElement.innerText = degrees;
  minutesElement.innerText = minutes;
}




// Handle continent selection
async function onContinentSelect(event) {
  const continentId = event.target.value;
  if (!continentId) {
    countriesSelect.innerHTML = '<option value="">Country</option>';
    citiesSelect.innerHTML = '<option value="">City</option>';
    hideCompass();
    return;
  }

  currentContinent = continents.find(c => c.id == continentId);
  currentCountry = null;
  currentCity = null;

  await populateCountriesDropdown(continentId);
  hideCompass();
}

// Handle country selection
async function onCountrySelect(event) {
  const countryId = event.target.value;
  if (!countryId) {
    citiesSelect.innerHTML = '<option value="">City</option>';
    hideCompass();
    return;
  }

  currentCountry = countries.find(c => c.id == countryId);
  currentCity = null;

  await populateCitiesDropdown(countryId);
  hideCompass();
}

function updateChosenCity(city) {
  plotCityName(city);
  plotOrientation(city);
  showCompass();
}

// Handle city selection
function onCitySelect(event) {
  const cityId = event.currentTarget.value;
  if (cityId) {
    const city = cities.find(city => city.id == cityId);
    if (city) {
      currentCity = city;

      // Store the complete location selection
      locationService.storeLocation(currentContinent, currentCountry, city);

      updateChosenCity(city);
    }
  } else {
    // If no city is selected, hide the compass
    hideCompass();
  }
}

function hideCompass() {
  compass.style.visibility = 'hidden';
  compass.style.height = '0px';
}

function showCompass() {
  compass.style.visibility = 'visible';
  compass.style.height = 'auto';
}

document.addEventListener('DOMContentLoaded', async function() {
  // Hide compass initially
  hideCompass();

  // Set up event listeners
  continentsSelect.addEventListener('change', onContinentSelect);
  countriesSelect.addEventListener('change', onCountrySelect);
  citiesSelect.addEventListener('change', onCitySelect);

  // Initialize location data
  await initializeLocationData();
});
