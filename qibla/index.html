<head>


  <style>
    :root {
      /* Tavoos Brand Colors */
      --tavoos-primary: rgb(0, 121, 107);
      --tavoos-dark: rgb(0, 90, 80);
      --tavoos-darker: rgb(0, 70, 60);
      --tavoos-light: rgb(0, 200, 180);

      /* UI Colors */
      --text-primary: white;
      --text-secondary: rgba(255, 255, 255, 0.8);
      --bg-overlay: rgba(255, 255, 255, 0.1);
      --border-light: rgba(255, 255, 255, 0.2);
      --border-medium: rgba(255, 255, 255, 0.3);
      --highlight-bg: rgba(255, 255, 255, 0.25);
    }

    a {
      color: var(--text-primary);
      font-weight: bold;
      text-decoration: none;
    }

    a:hover {
      color: var(--tavoos-light);
    }

    body {
      background-color: var(--tavoos-primary);
      color: var(--text-primary);
      width: 250px;
      min-height: 250px;
      padding: 1em;
    }

    select {
      background: var(--bg-overlay);
      padding: .5em;
      color: var(--text-primary);
      border: thin solid var(--border-medium);
      border-radius: .5em;
    }

    .compass {
      width: 180px;
      height: 180px;
      background-image: url('images/compass_rose.png');
      background-position: center;
      background-repeat: no-repeat;
      background-size: contain;
      margin: 15px auto;
      position: relative;
      transition: all 0.3s ease;
      display: block;
    }

    .compass:hover {
      transform: scale(1.05);
    }

    .hand {
      width: 70px;
      border: 2px solid black;
      background-color: black;
      position: absolute;
      transform-origin: center left;
      z-index: 10;
    }

    .hand::after {
      content: '';
      width: 10px;
      border: 1em solid transparent;
      border-left: 1em solid black;
      position: absolute;
      right: -1em;
      top: -1em;
      z-index: 10;
    }

    .logo-link {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.5em;
      margin-bottom: 3em;
      text-decoration: none;
      padding: 5px 10px;
      border-radius: 8px;
      transition: background-color 0.3s;
    }

    .logo-link:hover {
      background-color: var(--bg-overlay);
    }

    .logo-img {
      height: 32px;
      width: 32px;
      border-radius: 6px;
    }

    .logo-text {
      margin: 0;
      color: var(--text-primary);
    }
  </style>
</head>

<body>
  <a href="https://tavoos.eu" title="Visit Tavoos Website" class="logo-link">
    <img src="../android-chrome-192x192.png" alt="Tavoos Logo" class="logo-img">
    <h1 class="logo-text">Tavoos</h1>
  </a>

  <div style="display: flex; gap: .5em; margin: 1.5em 0">
    <select name="continents" id="continents" style="width: 33%">
      <option value="">Continent</option>
    </select>

    <select name="countries" id="countries" style="width: 33%">
      <option value="">Country</option>
    </select>

      <select name="cities" id="cities" style="width: 33%">
      <option value="">City</option>
    </select>
  </div>

  <div id="compass" class="compass"></div>
  <div id="bearing" style="text-align: center">
    <span id="degrees" ></span>
    &deg;
    <span id="minutes" style="margin-left: .1em"></span>
    '
  </div>



  <div style="text-align: center">
    <h1 id="city-box" />
  </div>

  <script src="../newtab/location-service.js"></script>
  <script src="script.js"></script>
</body>