<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Tavoos - Qibla Direction</title>
  <style>
    :root {
      /* Tavoos Brand Colors */
      --tavoos-primary: rgb(0, 121, 107);
      --tavoos-dark: rgb(0, 90, 80);
      --tavoos-darker: rgb(0, 70, 60);
      --tavoos-light: rgb(0, 200, 180);

      /* UI Colors */
      --text-primary: white;
      --text-secondary: rgba(255, 255, 255, 0.8);
      --bg-overlay: rgba(255, 255, 255, 0.1);
      --border-light: rgba(255, 255, 255, 0.2);
      --border-medium: rgba(255, 255, 255, 0.3);
      --highlight-bg: rgba(255, 255, 255, 0.25);
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      background-color: var(--tavoos-primary);
      color: var(--text-primary);
      width: 250px;
      min-height: 250px;
      padding: 1em;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    a {
      color: var(--text-primary);
      font-weight: bold;
      text-decoration: none;
    }

    a:hover {
      color: var(--tavoos-light);
    }
  </style>
  <script type="module" crossorigin src="./popup.js"></script>
  <link rel="stylesheet" crossorigin href="./popup.css">
</head>
<body>
  <div id="app"></div>
</body>
</html>
