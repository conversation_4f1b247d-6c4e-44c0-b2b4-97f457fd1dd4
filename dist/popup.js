(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const o of i)if(o.type==="childList")for(const a of o.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&r(a)}).observe(document,{childList:!0,subtree:!0});function n(i){const o={};return i.integrity&&(o.integrity=i.integrity),i.referrerPolicy&&(o.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?o.credentials="include":i.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(i){if(i.ep)return;i.ep=!0;const o=n(i);fetch(i.href,o)}})();const ln="5";typeof window<"u"&&((window.__svelte??={}).v??=new Set).add(ln);let ht=!1,an=!1;function sn(){ht=!0}sn();const un=1,fn=2,cn=16,vn=2,_n=4,dn=8,hn=1,pn=2,O=Symbol(),ge=!1;var Rt=Array.isArray,gn=Array.prototype.indexOf,De=Array.from,yn=Object.defineProperty,St=Object.getOwnPropertyDescriptor,wn=Object.getOwnPropertyDescriptors,mn=Object.prototype,bn=Array.prototype,Fe=Object.getPrototypeOf;const zt=()=>{};function En(t){return t()}function Xt(t){for(var e=0;e<t.length;e++)t[e]()}const k=2,re=4,mt=8,ie=16,X=32,bt=64,oe=128,P=256,Ot=512,F=1024,Q=2048,it=4096,Y=8192,qt=16384,Me=32768,le=65536,ye=1<<17,Cn=1<<18,Ne=1<<19,Zt=1<<20,ae=1<<21,W=Symbol("$state"),xn=Symbol("legacy props"),Pe=new class extends Error{name="StaleReactionError";message="The reaction that called `getAbortSignal()` was re-run or destroyed"};function ke(t){return t===this.v}function Re(t,e){return t!=t?e==e:t!==e||t!==null&&typeof t=="object"||typeof t=="function"}function qe(t){return!Re(t,this.v)}function Tn(t){throw new Error("https://svelte.dev/e/effect_in_teardown")}function Sn(){throw new Error("https://svelte.dev/e/effect_in_unowned_derived")}function Ln(t){throw new Error("https://svelte.dev/e/effect_orphan")}function An(){throw new Error("https://svelte.dev/e/effect_update_depth_exceeded")}function On(){throw new Error("https://svelte.dev/e/state_descriptors_fixed")}function In(){throw new Error("https://svelte.dev/e/state_prototype_fixed")}function Dn(){throw new Error("https://svelte.dev/e/state_unsafe_mutation")}function Fn(t){throw new Error("https://svelte.dev/e/lifecycle_outside_component")}let L=null;function we(t){L=t}function se(t,e=!1,n){L={p:L,c:null,e:null,s:t,x:null,l:null},ht&&!e&&(L.l={s:null,u:null,r1:[],r2:yt(!1)})}function ue(t){var e=L,n=e.e;if(n!==null){e.e=null;for(var r of n)He(r)}return L=e.p,{}}function jt(){return!ht||L!==null&&L.l===null}function ct(t){if(typeof t!="object"||t===null||W in t)return t;const e=Fe(t);if(e!==mn&&e!==bn)return t;var n=new Map,r=Rt(t),i=J(0),o=st,a=u=>{if(st===o)return u();var s=E,l=st;rt(null),Ce(o);var f=u();return rt(s),Ce(l),f};return r&&n.set("length",J(t.length)),new Proxy(t,{defineProperty(u,s,l){(!("value"in l)||l.configurable===!1||l.enumerable===!1||l.writable===!1)&&On();var f=n.get(s);return f===void 0?f=a(()=>{var _=J(l.value);return n.set(s,_),_}):S(f,l.value,!0),!0},deleteProperty(u,s){var l=n.get(s);if(l===void 0){if(s in u){const c=a(()=>J(O));n.set(s,c),Vt(i)}}else{if(r&&typeof s=="string"){var f=n.get("length"),_=Number(s);Number.isInteger(_)&&_<f.v&&S(f,_)}S(l,O),Vt(i)}return!0},get(u,s,l){if(s===W)return t;var f=n.get(s),_=s in u;if(f===void 0&&(!_||St(u,s)?.writable)&&(f=a(()=>{var v=ct(_?u[s]:O),p=J(v);return p}),n.set(s,f)),f!==void 0){var c=d(f);return c===O?void 0:c}return Reflect.get(u,s,l)},getOwnPropertyDescriptor(u,s){var l=Reflect.getOwnPropertyDescriptor(u,s);if(l&&"value"in l){var f=n.get(s);f&&(l.value=d(f))}else if(l===void 0){var _=n.get(s),c=_?.v;if(_!==void 0&&c!==O)return{enumerable:!0,configurable:!0,value:c,writable:!0}}return l},has(u,s){if(s===W)return!0;var l=n.get(s),f=l!==void 0&&l.v!==O||Reflect.has(u,s);if(l!==void 0||b!==null&&(!f||St(u,s)?.writable)){l===void 0&&(l=a(()=>{var c=f?ct(u[s]):O,v=J(c);return v}),n.set(s,l));var _=d(l);if(_===O)return!1}return f},set(u,s,l,f){var _=n.get(s),c=s in u;if(r&&s==="length")for(var v=l;v<_.v;v+=1){var p=n.get(v+"");p!==void 0?S(p,O):v in u&&(p=a(()=>J(O)),n.set(v+"",p))}if(_===void 0)(!c||St(u,s)?.writable)&&(_=a(()=>J(void 0)),S(_,ct(l)),n.set(s,_));else{c=_.v!==O;var x=a(()=>ct(l));S(_,x)}var g=Reflect.getOwnPropertyDescriptor(u,s);if(g?.set&&g.set.call(f,l),!c){if(r&&typeof s=="string"){var y=n.get("length"),h=Number(s);Number.isInteger(h)&&h>=y.v&&S(y,h+1)}Vt(i)}return!0},ownKeys(u){d(i);var s=Reflect.ownKeys(u).filter(_=>{var c=n.get(_);return c===void 0||c.v!==O});for(var[l,f]of n)f.v!==O&&!(l in u)&&s.push(l);return s},setPrototypeOf(){In()}})}function me(t){try{if(t!==null&&typeof t=="object"&&W in t)return t[W]}catch{}return t}function Mn(t,e){return Object.is(me(t),me(e))}function fe(t){var e=k|Q,n=E!==null&&(E.f&k)!==0?E:null;return b===null||n!==null&&(n.f&P)!==0?e|=P:b.f|=Ne,{ctx:L,deps:null,effects:null,equals:ke,f:e,fn:t,reactions:null,rv:0,v:O,wv:0,parent:n??b,ac:null}}function je(t){const e=fe(t);return e.equals=qe,e}function Ue(t){var e=t.effects;if(e!==null){t.effects=null;for(var n=0;n<e.length;n+=1)pt(e[n])}}function Nn(t){for(var e=t.parent;e!==null;){if((e.f&k)===0)return e;e=e.parent}return null}function ce(t){var e,n=b;dt(Nn(t));try{Ue(t),e=nn(t)}finally{dt(n)}return e}function $e(t){var e=ce(t);if(t.equals(e)||(t.v=e,t.wv=tn()),!nt){var n=(et||(t.f&P)!==0)&&t.deps!==null?it:F;U(t,n)}}const lt=new Map;function yt(t,e){var n={f:0,v:t,reactions:null,equals:ke,rv:0,wv:0};return n}function J(t,e){const n=yt(t);return Xn(n),n}function K(t,e=!1,n=!0){const r=yt(t);return e||(r.equals=qe),ht&&n&&L!==null&&L.l!==null&&(L.l.s??=[]).push(r),r}function Pn(t,e){return S(t,Ct(()=>d(t))),e}function S(t,e,n=!1){E!==null&&(!B||(E.f&ye)!==0)&&jt()&&(E.f&(k|ie|ye))!==0&&!G?.includes(t)&&Dn();let r=n?ct(e):e;return Be(t,r)}function Be(t,e){if(!t.equals(e)){var n=t.v;nt?lt.set(t,e):lt.set(t,n),t.v=e,(t.f&k)!==0&&((t.f&Q)!==0&&ce(t),U(t,(t.f&P)===0?F:it)),t.wv=tn(),Ke(t,Q),jt()&&b!==null&&(b.f&F)!==0&&(b.f&(X|bt))===0&&(q===null?Zn([t]):q.push(t))}return e}function Vt(t){S(t,t.v+1)}function Ke(t,e){var n=t.reactions;if(n!==null)for(var r=jt(),i=n.length,o=0;o<i;o++){var a=n[o],u=a.f;(u&Q)===0&&(!r&&a===b||(U(a,e),(u&(F|P))!==0&&((u&k)!==0?Ke(a,it):Bt(a))))}}function kn(){console.warn("https://svelte.dev/e/select_multiple_invalid_value")}let Rn=!1;var qn,jn,Un;function It(t){return jn.call(t)}function Ut(t){return Un.call(t)}function N(t,e){return It(t)}function be(t,e){{var n=It(t);return n instanceof Comment&&n.data===""?Ut(n):n}}function $(t,e=1,n=!1){let r=t;for(;e--;)r=Ut(r);return r}function $n(t){t.textContent=""}function ze(t){b===null&&E===null&&Ln(),E!==null&&(E.f&P)!==0&&b===null&&Sn(),nt&&Tn()}function Bn(t,e){var n=e.last;n===null?e.last=e.first=t:(n.next=t,t.prev=n,e.last=t)}function ut(t,e,n,r=!0){var i=b,o={ctx:L,deps:null,nodes_start:null,nodes_end:null,f:t|Q,first:null,fn:e,last:null,next:null,parent:i,b:i&&i.b,prev:null,teardown:null,transitions:null,wv:0,ac:null};if(n)try{$t(o),o.f|=Me}catch(s){throw pt(o),s}else e!==null&&Bt(o);var a=n&&o.deps===null&&o.first===null&&o.nodes_start===null&&o.teardown===null&&(o.f&(Ne|oe))===0;if(!a&&r&&(i!==null&&Bn(o,i),E!==null&&(E.f&k)!==0)){var u=E;(u.effects??=[]).push(o)}return o}function Ve(t){const e=ut(mt,null,!1);return U(e,F),e.teardown=t,e}function Jt(t){if(ze(),!E&&b&&(b.f&X)!==0){var e=L;(e.e??=[]).push(t)}else return He(t)}function He(t){return ut(re|ae,t,!1)}function Kn(t){return ze(),ut(mt|ae,t,!0)}function zn(t){return ut(re,t,!1)}function Vn(t,e){var n=L,r={effect:null,ran:!1};n.l.r1.push(r),r.effect=ve(()=>{t(),!r.ran&&(r.ran=!0,S(n.l.r2,!0),Ct(e))})}function Hn(){var t=L;ve(()=>{if(d(t.l.r2)){for(var e of t.l.r1){var n=e.effect;(n.f&F)!==0&&U(n,it),Et(n)&&$t(n),e.ran=!1}t.l.r2.v=!1}})}function ve(t){return ut(mt,t,!0)}function vt(t,e=[],n=fe){const r=e.map(n);return _e(()=>t(...r.map(d)))}function _e(t,e=0){var n=ut(mt|ie|e,t,!0);return n}function Dt(t,e=!0){return ut(mt|X,t,!0,e)}function Ye(t){var e=t.teardown;if(e!==null){const n=nt,r=E;Ee(!0),rt(null);try{e.call(null)}finally{Ee(n),rt(r)}}}function We(t,e=!1){var n=t.first;for(t.first=t.last=null;n!==null;){n.ac?.abort(Pe);var r=n.next;(n.f&bt)!==0?n.parent=null:pt(n,e),n=r}}function Yn(t){for(var e=t.first;e!==null;){var n=e.next;(e.f&X)===0&&pt(e),e=n}}function pt(t,e=!0){var n=!1;(e||(t.f&Cn)!==0)&&t.nodes_start!==null&&t.nodes_end!==null&&(Wn(t.nodes_start,t.nodes_end),n=!0),We(t,e&&!n),kt(t,0),U(t,qt);var r=t.transitions;if(r!==null)for(const o of r)o.stop();Ye(t);var i=t.parent;i!==null&&i.first!==null&&Ge(t),t.next=t.prev=t.teardown=t.ctx=t.deps=t.fn=t.nodes_start=t.nodes_end=t.ac=null}function Wn(t,e){for(;t!==null;){var n=t===e?null:Ut(t);t.remove(),t=n}}function Ge(t){var e=t.parent,n=t.prev,r=t.next;n!==null&&(n.next=r),r!==null&&(r.prev=n),e!==null&&(e.first===t&&(e.first=r),e.last===t&&(e.last=n))}function te(t,e){var n=[];de(t,n,!0),Qe(n,()=>{pt(t),e&&e()})}function Qe(t,e){var n=t.length;if(n>0){var r=()=>--n||e();for(var i of t)i.out(r)}else e()}function de(t,e,n){if((t.f&Y)===0){if(t.f^=Y,t.transitions!==null)for(const a of t.transitions)(a.is_global||n)&&e.push(a);for(var r=t.first;r!==null;){var i=r.next,o=(r.f&le)!==0||(r.f&X)!==0;de(r,e,o?n:!1),r=i}}}function Ft(t){Xe(t,!0)}function Xe(t,e){if((t.f&Y)!==0){t.f^=Y;for(var n=t.first;n!==null;){var r=n.next,i=(n.f&le)!==0||(n.f&X)!==0;Xe(n,i?e:!1),n=r}if(t.transitions!==null)for(const o of t.transitions)(o.is_global||e)&&o.in()}}let Mt=[];function Gn(){var t=Mt;Mt=[],Xt(t)}function Ze(t){Mt.length===0&&queueMicrotask(Gn),Mt.push(t)}function Qn(t){var e=b;if((e.f&Me)===0){if((e.f&oe)===0)throw t;e.fn(t)}else Je(t,e)}function Je(t,e){for(;e!==null;){if((e.f&oe)!==0)try{e.b.error(t);return}catch{}e=e.parent}throw t}let ee=!1,Nt=null,at=!1,nt=!1;function Ee(t){nt=t}let Lt=[];let E=null,B=!1;function rt(t){E=t}let b=null;function dt(t){b=t}let G=null;function Xn(t){E!==null&&E.f&Zt&&(G===null?G=[t]:G.push(t))}let D=null,M=0,q=null;function Zn(t){q=t}let Pt=1,wt=0,st=wt;function Ce(t){st=t}let et=!1;function tn(){return++Pt}function Et(t){var e=t.f;if((e&Q)!==0)return!0;if((e&it)!==0){var n=t.deps,r=(e&P)!==0;if(n!==null){var i,o,a=(e&Ot)!==0,u=r&&b!==null&&!et,s=n.length;if(a||u){var l=t,f=l.parent;for(i=0;i<s;i++)o=n[i],(a||!o?.reactions?.includes(l))&&(o.reactions??=[]).push(l);a&&(l.f^=Ot),u&&f!==null&&(f.f&P)===0&&(l.f^=P)}for(i=0;i<s;i++)if(o=n[i],Et(o)&&$e(o),o.wv>t.wv)return!0}(!r||b!==null&&!et)&&U(t,F)}return!1}function en(t,e,n=!0){var r=t.reactions;if(r!==null&&!G?.includes(t))for(var i=0;i<r.length;i++){var o=r[i];(o.f&k)!==0?en(o,e,!1):e===o&&(n?U(o,Q):(o.f&F)!==0&&U(o,it),Bt(o))}}function nn(t){var e=D,n=M,r=q,i=E,o=et,a=G,u=L,s=B,l=st,f=t.f;D=null,M=0,q=null,et=(f&P)!==0&&(B||!at||E===null),E=(f&(X|bt))===0?t:null,G=null,we(t.ctx),B=!1,st=++wt,t.f|=Zt,t.ac!==null&&(t.ac.abort(Pe),t.ac=null);try{var _=(0,t.fn)(),c=t.deps;if(D!==null){var v;if(kt(t,M),c!==null&&M>0)for(c.length=M+D.length,v=0;v<D.length;v++)c[M+v]=D[v];else t.deps=c=D;if(!et||(f&k)!==0&&t.reactions!==null)for(v=M;v<c.length;v++)(c[v].reactions??=[]).push(t)}else c!==null&&M<c.length&&(kt(t,M),c.length=M);if(jt()&&q!==null&&!B&&c!==null&&(t.f&(k|it|Q))===0)for(v=0;v<q.length;v++)en(q[v],t);return i!==null&&i!==t&&(wt++,q!==null&&(r===null?r=q:r.push(...q))),_}catch(p){Qn(p)}finally{D=e,M=n,q=r,E=i,et=o,G=a,we(u),B=s,st=l,t.f^=Zt}}function Jn(t,e){let n=e.reactions;if(n!==null){var r=gn.call(n,t);if(r!==-1){var i=n.length-1;i===0?n=e.reactions=null:(n[r]=n[i],n.pop())}}n===null&&(e.f&k)!==0&&(D===null||!D.includes(e))&&(U(e,it),(e.f&(P|Ot))===0&&(e.f^=Ot),Ue(e),kt(e,0))}function kt(t,e){var n=t.deps;if(n!==null)for(var r=e;r<n.length;r++)Jn(t,n[r])}function $t(t){var e=t.f;if((e&qt)===0){U(t,F);var n=b,r=at;b=t,at=!0;try{(e&ie)!==0?Yn(t):We(t),Ye(t);var i=nn(t);t.teardown=typeof i=="function"?i:null,t.wv=Pt;var o;ge&&an&&(t.f&Q)!==0&&t.deps}finally{at=r,b=n}}}function tr(){try{An()}catch(t){if(Nt!==null)Je(t,Nt);else throw t}}function er(){var t=at;try{var e=0;for(at=!0;Lt.length>0;){e++>1e3&&tr();var n=Lt,r=n.length;Lt=[];for(var i=0;i<r;i++){var o=rr(n[i]);nr(o)}lt.clear()}}finally{ee=!1,at=t,Nt=null}}function nr(t){var e=t.length;if(e!==0){for(var n=0;n<e;n++){var r=t[n];if((r.f&(qt|Y))===0&&Et(r)){var i=Pt;if($t(r),r.deps===null&&r.first===null&&r.nodes_start===null&&(r.teardown===null?Ge(r):r.fn=null),Pt>i&&(r.f&ae)!==0)break}}for(;n<e;n+=1)Bt(t[n])}}function Bt(t){ee||(ee=!0,queueMicrotask(er));for(var e=Nt=t;e.parent!==null;){e=e.parent;var n=e.f;if((n&(bt|X))!==0){if((n&F)===0)return;e.f^=F}}Lt.push(e)}function rr(t){for(var e=[],n=t;n!==null;){var r=n.f,i=(r&(X|bt))!==0,o=i&&(r&F)!==0;if(!o&&(r&Y)===0){(r&re)!==0?e.push(n):i?n.f^=F:Et(n)&&$t(n);var a=n.first;if(a!==null){n=a;continue}}var u=n.parent;for(n=n.next;n===null&&u!==null;)n=u.next,u=u.parent}return e}function d(t){var e=t.f,n=(e&k)!==0;if(E!==null&&!B){if(!G?.includes(t)){var r=E.deps;t.rv<wt&&(t.rv=wt,D===null&&r!==null&&r[M]===t?M++:D===null?D=[t]:(!et||!D.includes(t))&&D.push(t))}}else if(n&&t.deps===null&&t.effects===null){var i=t,o=i.parent;o!==null&&(o.f&P)===0&&(i.f^=P)}if(n&&!nt&&(i=t,Et(i)&&$e(i)),nt){if(lt.has(t))return lt.get(t);if(n){i=t;var a=i.v;return((i.f&F)!==0||rn(i))&&(a=ce(i)),lt.set(i,a),a}}return t.v}function rn(t){if(t.v===O)return!0;if(t.deps===null)return!1;for(const e of t.deps)if(lt.has(e)||(e.f&k)!==0&&rn(e))return!0;return!1}function Ct(t){var e=B;try{return B=!0,t()}finally{B=e}}const ir=-7169;function U(t,e){t.f=t.f&ir|e}function on(t){if(!(typeof t!="object"||!t||t instanceof EventTarget)){if(W in t)ne(t);else if(!Array.isArray(t))for(let e in t){const n=t[e];typeof n=="object"&&n&&W in n&&ne(n)}}}function ne(t,e=new Set){if(typeof t=="object"&&t!==null&&!(t instanceof EventTarget)&&!e.has(t)){e.add(t),t instanceof Date&&t.getTime();for(let r in t)try{ne(t[r],e)}catch{}const n=Fe(t);if(n!==Object.prototype&&n!==Array.prototype&&n!==Map.prototype&&n!==Set.prototype&&n!==Date.prototype){const r=wn(n);for(let i in r){const o=r[i].get;if(o)try{o.call(t)}catch{}}}}}function or(t){var e=E,n=b;rt(null),dt(null);try{return t()}finally{rt(e),dt(n)}}function lr(t,e,n,r={}){function i(o){if(r.capture||ar.call(e,o),!o.cancelBubble)return or(()=>n?.call(this,o))}return t.startsWith("pointer")||t.startsWith("touch")||t==="wheel"?Ze(()=>{e.addEventListener(t,i,r)}):e.addEventListener(t,i,r),i}function Ht(t,e,n,r,i){var o={capture:r,passive:i},a=lr(t,e,n,o);(e===document.body||e===window||e===document||e instanceof HTMLMediaElement)&&Ve(()=>{e.removeEventListener(t,a,o)})}function ar(t){var e=this,n=e.ownerDocument,r=t.type,i=t.composedPath?.()||[],o=i[0]||t.target,a=0,u=t.__root;if(u){var s=i.indexOf(u);if(s!==-1&&(e===document||e===window)){t.__root=e;return}var l=i.indexOf(e);if(l===-1)return;s<=l&&(a=s)}if(o=i[a]||t.target,o!==e){yn(t,"currentTarget",{configurable:!0,get(){return o||n}});var f=E,_=b;rt(null),dt(null);try{for(var c,v=[];o!==null;){var p=o.assignedSlot||o.parentNode||o.host||null;try{var x=o["__"+r];if(x!=null&&(!o.disabled||t.target===o))if(Rt(x)){var[g,...y]=x;g.apply(o,[t,...y])}else x.call(o,t)}catch(h){c?v.push(h):c=h}if(t.cancelBubble||p===e||p===null)break;o=p}if(c){for(let h of v)queueMicrotask(()=>{throw h});throw c}}finally{t.__root=e,delete t.currentTarget,rt(f),dt(_)}}}function sr(t){var e=document.createElement("template");return e.innerHTML=t.replaceAll("<!>","<!---->"),e.content}function xe(t,e){var n=b;n.nodes_start===null&&(n.nodes_start=t,n.nodes_end=e)}function V(t,e){var n=(e&hn)!==0,r=(e&pn)!==0,i,o=!t.startsWith("<!>");return()=>{i===void 0&&(i=sr(o?t:"<!>"+t),n||(i=It(i)));var a=r||qn?document.importNode(i,!0):i.cloneNode(!0);if(n){var u=It(a),s=a.lastChild;xe(u,s)}else xe(a,a);return a}}function z(t,e){t!==null&&t.before(e)}function _t(t,e){var n=e==null?"":typeof e=="object"?e+"":e;n!==(t.__t??=t.nodeValue)&&(t.__t=n,t.nodeValue=n+"")}function he(t){L===null&&Fn(),ht&&L.l!==null?ur(L).m.push(t):Jt(()=>{const e=Ct(t);if(typeof e=="function")return e})}function ur(t){var e=t.l;return e.u??={a:[],b:[],m:[]}}function Te(t,e,[n,r]=[0,0]){var i=t,o=null,a=null,u=O,s=n>0?le:0,l=!1;const f=(c,v=!0)=>{l=!0,_(v,c)},_=(c,v)=>{u!==(u=c)&&(u?(o?Ft(o):v&&(o=Dt(()=>v(i))),a&&te(a,()=>{a=null})):(a?Ft(a):v&&(a=Dt(()=>v(i,[n+1,r]))),o&&te(o,()=>{o=null})))};_e(()=>{l=!1,e(f),l||_(null,null)},s)}function Yt(t,e){return e}function fr(t,e,n,r){for(var i=[],o=e.length,a=0;a<o;a++)de(e[a].e,i,!0);var u=o>0&&i.length===0&&n!==null;if(u){var s=n.parentNode;$n(s),s.append(n),r.clear(),tt(t,e[0].prev,e[o-1].next)}Qe(i,()=>{for(var l=0;l<o;l++){var f=e[l];u||(r.delete(f.k),tt(t,f.prev,f.next)),pt(f.e,!u)}})}function Wt(t,e,n,r,i,o=null){var a=t,u={flags:e,items:new Map,first:null},s=null,l=!1,f=je(()=>{var _=n();return Rt(_)?_:_==null?[]:De(_)});_e(()=>{var _=d(f),c=_.length;l&&c===0||(l=c===0,cr(_,u,a,i,e,r,n),o!==null&&(c===0?s?Ft(s):s=Dt(()=>o(a)):s!==null&&te(s,()=>{s=null})),d(f))})}function cr(t,e,n,r,i,o,a){var u=t.length,s=e.items,l=e.first,f=l,_,c=null,v=[],p=[],x,g,y,h;for(h=0;h<u;h+=1){if(x=t[h],g=o(x,h),y=s.get(g),y===void 0){var j=f?f.e.nodes_start:n;c=_r(j,e,c,c===null?e.first:c.next,x,g,h,r,i,a),s.set(g,c),v=[],p=[],f=c.next;continue}if(vr(y,x,h),(y.e.f&Y)!==0&&Ft(y.e),y!==f){if(_!==void 0&&_.has(y)){if(v.length<p.length){var A=p[0],I;c=A.prev;var Z=v[0],H=v[v.length-1];for(I=0;I<v.length;I+=1)Se(v[I],A,n);for(I=0;I<p.length;I+=1)_.delete(p[I]);tt(e,Z.prev,H.next),tt(e,c,Z),tt(e,H,A),f=A,c=H,h-=1,v=[],p=[]}else _.delete(y),Se(y,f,n),tt(e,y.prev,y.next),tt(e,y,c===null?e.first:c.next),tt(e,c,y),c=y;continue}for(v=[],p=[];f!==null&&f.k!==g;)(f.e.f&Y)===0&&(_??=new Set).add(f),p.push(f),f=f.next;if(f===null)continue;y=f}v.push(y),c=y,f=y.next}if(f!==null||_!==void 0){for(var w=_===void 0?[]:De(_);f!==null;)(f.e.f&Y)===0&&w.push(f),f=f.next;var m=w.length;if(m>0){var C=null;fr(e,w,C,s)}}b.first=e.first&&e.first.e,b.last=c&&c.e}function vr(t,e,n,r){Be(t.v,e),t.i=n}function _r(t,e,n,r,i,o,a,u,s,l){var f=(s&un)!==0,_=(s&cn)===0,c=f?_?K(i,!1,!1):yt(i):i,v=(s&fn)===0?a:yt(a),p={i:v,v:c,k:o,a:null,e:null,prev:n,next:r};try{return p.e=Dt(()=>u(t,c,v,l),Rn),p.e.prev=n&&n.e,p.e.next=r&&r.e,n===null?e.first=p:(n.next=p,n.e.next=p.e),r!==null&&(r.prev=p,r.e.prev=p.e),p}finally{}}function Se(t,e,n){for(var r=t.next?t.next.e.nodes_start:n,i=e?e.e.nodes_start:n,o=t.e.nodes_start;o!==r;){var a=Ut(o);i.before(o),o=a}}function tt(t,e,n){e===null?t.first=n:(e.next=n,e.e.next=n&&n.e),n!==null&&(n.prev=e,n.e.prev=e&&e.e)}function At(t,e,n=!1){if(t.multiple){if(e==null)return;if(!Rt(e))return kn();for(var r of t.options)r.selected=e.includes(Le(r));return}for(r of t.options){var i=Le(r);if(Mn(i,e)){r.selected=!0;return}}(!n||e!==void 0)&&(t.selectedIndex=-1)}function Gt(t){var e=new MutationObserver(()=>{At(t,t.__value)});e.observe(t,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["value"]}),Ve(()=>{e.disconnect()})}function Le(t){return"__value"in t?t.__value:t.value}function Ae(t,e){return t===e||t?.[W]===e}function dr(t={},e,n,r){return zn(()=>{var i,o;return ve(()=>{i=o,o=[],Ct(()=>{t!==n(...o)&&(e(t,...o),i&&Ae(n(...i),t)&&e(null,...i))})}),()=>{Ze(()=>{o&&Ae(n(...o),t)&&e(null,...o)})}}),t}function pe(t=!1){const e=L,n=e.l.u;if(!n)return;let r=()=>on(e.s);if(t){let i=0,o={};const a=fe(()=>{let u=!1;const s=e.s;for(const l in s)s[l]!==o[l]&&(o[l]=s[l],u=!0);return u&&i++,i});r=()=>d(a)}n.b.length&&Kn(()=>{Oe(e,r),Xt(n.b)}),Jt(()=>{const i=Ct(()=>n.m.map(En));return()=>{for(const o of i)typeof o=="function"&&o()}}),n.a.length&&Jt(()=>{Oe(e,r),Xt(n.a)})}function Oe(t,e){if(t.l.s)for(const n of t.l.s)d(n);e()}const ft=[];function Kt(t,e=zt){let n=null;const r=new Set;function i(u){if(Re(t,u)&&(t=u,n)){const s=!ft.length;for(const l of r)l[1](),ft.push(l,t);if(s){for(let l=0;l<ft.length;l+=2)ft[l][0](ft[l+1]);ft.length=0}}}function o(u){i(u(t))}function a(u,s=zt){const l=[u,s];return r.add(l),r.size===1&&(n=e(i,o)||zt),u(t),()=>{r.delete(l),r.size===0&&n&&(n(),n=null)}}return{set:i,update:o,subscribe:a}}let xt=!1;function hr(t){var e=xt;try{return xt=!1,[t(),xt]}finally{xt=e}}function pr(t,e,n,r){var i=!ht||(n&vn)!==0,o=(n&dn)!==0,a=r,u=!0,s=()=>(u&&(u=!1,a=r),a),l;{var f=W in t||xn in t;l=St(t,e)?.set??(f&&e in t?h=>t[e]=h:void 0)}var _,c=!1;[_,c]=hr(()=>t[e]);var v;if(i?v=()=>{var h=t[e];return h===void 0?s():(u=!0,h)}:v=()=>{var h=t[e];return h!==void 0&&(a=void 0),h===void 0?a:h},i&&(n&_n)===0)return v;if(l){var p=t.$$legacy;return function(h,j){return arguments.length>0?((!i||!j||p||c)&&l(j?v():h),h):v()}}var x=!1,g=je(()=>(x=!1,v()));d(g);var y=b;return function(h,j){if(arguments.length>0){const A=j?d(g):i&&o?ct(h):h;return S(g,A),x=!0,a!==void 0&&(a=A),h}return nt&&x||(y.f&qt)!==0?g.v:d(g)}}const ot=Kt({continent:null,country:null,city:null}),Ie=Kt([]),Qt=Kt([]),Tt=Kt([]);class gr{constructor(){this.baseUrl="https://behnegar.app/api/v1/tavoos/pray-times-v2",this.storageKeys={continents:"tavoos_continents",selectedContinent:"tavoos_selected_continent",selectedCountry:"tavoos_selected_country",selectedCity:"tavoos_selected_city"},this.defaults={continent:{code:"EU",name:"Europe"},country:{europe:{code:"BE",name:"Belgium"},asia:{code:"IR",name:"Iran"}},city:{name:"Gent"}}}async getContinents(){try{const e=await this.getFromStorage(this.storageKeys.continents);if(e&&e.length>0)return e;const n=await fetch(`${this.baseUrl}/continents`);if(!n.ok)throw new Error(`Failed to fetch continents: ${n.status}`);const r=await n.json();try{await this.saveToStorage(this.storageKeys.continents,r)}catch(i){console.warn("Could not cache continents:",i)}return r}catch(e){return console.error("Error fetching continents:",e),[]}}async getCountries(e){try{const n=await fetch(`${this.baseUrl}/continents/${e}/countries`);if(!n.ok)throw new Error(`Failed to fetch countries: ${n.status}`);return await n.json()}catch(n){return console.error("Error fetching countries:",n),[]}}async getCities(e){try{const n=await fetch(`${this.baseUrl}/countries/${e}/cities`);if(!n.ok)throw new Error(`Failed to fetch cities: ${n.status}`);return await n.json()}catch(n){return console.error("Error fetching cities:",n),[]}}async getStoredLocation(){try{let e=null,n=null,r=null;try{e=await this.getFromStorage(this.storageKeys.selectedContinent)}catch(i){console.warn("Could not retrieve continent from storage:",i)}try{n=await this.getFromStorage(this.storageKeys.selectedCountry)}catch(i){console.warn("Could not retrieve country from storage:",i)}try{r=await this.getFromStorage(this.storageKeys.selectedCity)}catch(i){console.warn("Could not retrieve city from storage:",i)}return{continent:e,country:n,city:r}}catch(e){return console.error("Error getting stored location:",e),{continent:null,country:null,city:null}}}async storeLocation(e,n,r){try{try{await this.saveToStorage(this.storageKeys.selectedContinent,e)}catch(i){console.warn("Could not save continent:",i)}try{await this.saveToStorage(this.storageKeys.selectedCountry,n)}catch(i){console.warn("Could not save country:",i)}try{await this.saveToStorage(this.storageKeys.selectedCity,r)}catch(i){console.warn("Could not save city:",i)}ot.set({continent:e,country:n,city:r})}catch(i){console.error("Error storing location:",i)}}async initializeDefaultLocation(){try{const e=await this.getContinents();if(e.length===0)throw new Error("No continents available");let n=e.find(u=>u.code===this.defaults.continent.code);n||(n=e[0]);const r=await this.getCountries(n.id);if(r.length===0)throw new Error("No countries available");let i;n.code==="EU"?i=r.find(u=>u.code===this.defaults.country.europe.code):n.code==="AS"&&(i=r.find(u=>u.code===this.defaults.country.asia.code)),i||(i=r[0]);const o=await this.getCities(i.id);if(o.length===0)throw new Error("No cities available");let a=o.find(u=>u.name===this.defaults.city.name);return a||(a=o[0]),await this.storeLocation(n,i,a),{continent:n,country:i,city:a}}catch(e){return console.error("Error initializing default location:",e),null}}async initializeLocationData(){try{const e=await this.getStoredLocation();return e.continent&&e.country&&e.city?(ot.set(e),e):await this.initializeDefaultLocation()}catch(e){return console.error("Error initializing location data:",e),null}}async getFromStorage(e){return new Promise((n,r)=>{chrome.storage.sync.get([e],i=>{chrome.runtime.lastError?r(new Error(chrome.runtime.lastError.message)):n(i[e]||null)})})}async saveToStorage(e,n){return new Promise((r,i)=>{chrome.storage.sync.set({[e]:n},()=>{chrome.runtime.lastError?i(new Error(chrome.runtime.lastError.message)):r()})})}}const gt=new gr;var yr=V('<option class="svelte-jnmml1"> </option>'),wr=V('<option class="svelte-jnmml1"> </option>'),mr=V('<option class="svelte-jnmml1"> </option>'),br=V('<div class="location-selector svelte-jnmml1"><select class="svelte-jnmml1"><option class="svelte-jnmml1">Continent</option><!></select> <select class="svelte-jnmml1"><option class="svelte-jnmml1">Country</option><!></select> <select class="svelte-jnmml1"><option class="svelte-jnmml1">City</option><!></select></div>');function Er(t,e){se(e,!1);let n=K([]),r=K([]),i=K([]),o=K({continent:null,country:null,city:null});Ie.subscribe(w=>S(n,w)),Qt.subscribe(w=>S(r,w)),Tt.subscribe(w=>S(i,w)),ot.subscribe(w=>S(o,w)),he(async()=>{const w=await gt.getContinents();Ie.set(w),d(o).continent&&(await l(d(o).continent.id),d(o).country&&await f(d(o).country.id))});async function a(w){const m=w.target.value;if(!m){Qt.set([]),Tt.set([]);return}const C=d(n).find(T=>T.id==m);await l(m),ot.update(T=>({...T,continent:C,country:null,city:null}))}async function u(w){const m=w.target.value;if(!m){Tt.set([]);return}const C=d(r).find(T=>T.id==m);await f(m),ot.update(T=>({...T,country:C,city:null}))}async function s(w){const m=w.target.value;if(!m)return;const C=d(i).find(T=>T.id==m);ot.update(T=>{const R={...T,city:C};return gt.storeLocation(R.continent,R.country,C),R})}async function l(w){const m=await gt.getCountries(w);Qt.set(m)}async function f(w){const m=await gt.getCities(w);Tt.set(m)}pe();var _=br(),c=N(_),v=N(c);v.value=v.__value="";var p=$(v);Wt(p,1,()=>d(n),Yt,(w,m)=>{var C=yr(),T=N(C),R={};vt(()=>{_t(T,d(m).name),R!==(R=d(m).id)&&(C.value=(C.__value=d(m).id)??"")}),z(w,C)});var x;Gt(c);var g=$(c,2),y=N(g);y.value=y.__value="";var h=$(y);Wt(h,1,()=>d(r),Yt,(w,m)=>{var C=wr(),T=N(C),R={};vt(()=>{_t(T,d(m).name),R!==(R=d(m).id)&&(C.value=(C.__value=d(m).id)??"")}),z(w,C)});var j;Gt(g);var A=$(g,2),I=N(A);I.value=I.__value="";var Z=$(I);Wt(Z,1,()=>d(i),Yt,(w,m)=>{var C=mr(),T=N(C),R={};vt(()=>{_t(T,d(m).name),R!==(R=d(m).id)&&(C.value=(C.__value=d(m).id)??"")}),z(w,C)});var H;Gt(A),vt(()=>{x!==(x=d(o).continent?.id||"")&&(c.value=(c.__value=d(o).continent?.id||"")??"",At(c,d(o).continent?.id||"")),j!==(j=d(o).country?.id||"")&&(g.value=(g.__value=d(o).country?.id||"")??"",At(g,d(o).country?.id||"")),H!==(H=d(o).city?.id||"")&&(A.value=(A.__value=d(o).city?.id||"")??"",At(A,d(o).city?.id||""))}),Ht("change",c,a),Ht("change",g,u),Ht("change",A,s),z(t,_),ue()}var Cr=V(`<div class="compass-container svelte-5la0lp"><div class="compass svelte-5la0lp"></div> <div class="bearing svelte-5la0lp"><span class="degrees svelte-5la0lp"> </span>° <span class="minutes svelte-5la0lp"> </span>'</div></div>`);function xr(t,e){se(e,!1);let n=pr(e,"city",8),r=K(),i=0,o=K(0),a=K(0);function u(g,y){const h=T=>T*Math.PI/180,j=T=>T*180/Math.PI,A=h(g),I=h(y),Z=h(21.3891),H=h(39.8579),w=Math.sin(H-I)*Math.cos(Z),m=Math.cos(A)*Math.sin(Z)-Math.sin(A)*Math.cos(Z)*Math.cos(H-I);return j(Math.atan2(w,m))+180}function s(){if(!(!n()||!n().latitude||!n().longitude)&&(i=u(n().latitude,n().longitude),S(o,Math.floor(i)),S(a,Math.floor(60*(i-d(o)))),d(r))){Pn(r,d(r).innerHTML="");const g=document.createElement("div");g.classList.add("hand"),g.style.transform=`translate(90px, 90px) translate(0, -2px) rotate(${i+90-11.5}deg)`,g.style.zIndex="20",d(r).appendChild(g)}}he(()=>{s()}),Vn(()=>on(n()),()=>{n()&&s()}),Hn(),pe();var l=Cr(),f=N(l);dr(f,g=>S(r,g),()=>d(r));var _=$(f,2),c=N(_),v=N(c),p=$(c,2),x=N(p);vt(()=>{_t(v,d(o)),_t(x,d(a))}),z(t,l),ue()}var Tr=V('<a href="https://tavoos.eu" title="Visit Tavoos Website" class="logo-link svelte-1yzytrq"><img src="./android-chrome-192x192.png" alt="Tavoos Logo" class="logo-img svelte-1yzytrq"/> <h1 class="logo-text svelte-1yzytrq">Tavoos</h1></a>');function Sr(t){var e=Tr();z(t,e)}var Lr=V('<div class="loading svelte-q2ix8">Loading...</div>'),Ar=V('<!> <div class="city-info svelte-q2ix8"> </div>',1),Or=V("<!> <!>",1),Ir=V('<main class="svelte-q2ix8"><!> <!></main>');function Dr(t,e){se(e,!1);let n=K(!0),r=K(null);ot.subscribe(l=>{S(r,l)}),he(async()=>{try{await gt.initializeLocationData(),S(n,!1)}catch(l){console.error("Error initializing popup:",l),S(n,!1)}}),pe();var i=Ir(),o=N(i);Sr(o);var a=$(o,2);{var u=l=>{var f=Lr();z(l,f)},s=l=>{var f=Or(),_=be(f);Er(_,{});var c=$(_,2);{var v=p=>{var x=Ar(),g=be(x);xr(g,{get city(){return d(r).city}});var y=$(g,2),h=N(y);vt(()=>_t(h,`${d(r).city.name??""}, ${d(r).country?.name||"Unknown"}`)),z(p,x)};Te(c,p=>{d(r)?.city&&p(v)})}z(l,f)};Te(a,l=>{d(n)?l(u):l(s,!1)})}z(t,i),ue()}new Dr({target:document.getElementById("app")});
