#!/bin/bash

# Tavoos Chrome Extension Packing Script
# This script builds the Svelte popup and packs the extension into a .zip file for submission to the Chrome Web Store

# Setup Node environment
echo "🔧 Setting up Node environment..."
source ~/.zshrc && nvm use

# Get the current version from manifest.json
VERSION=$(grep -o '"version": "[^"]*"' manifest.json | cut -d'"' -f4)
echo "📦 Packing Tavoos Extension version $VERSION"

# Build the Svelte popup
echo "🏗️  Building Svelte popup..."
npm run build:popup

if [ $? -ne 0 ]; then
    echo "❌ Build failed! Please fix the errors and try again."
    exit 1
fi

# Create releases directory if it doesn't exist
mkdir -p releases

# Create a zip file with all necessary files
# Exclude development files, git files, source files, and build artifacts
echo "📦 Creating extension package..."
zip -r "releases/tavoos-extension-v$VERSION.zip" . \
    -x "*.git*" \
    -x "*.DS_Store" \
    -x "pack.sh" \
    -x "*.zip" \
    -x "*.pem" \
    -x "*.crx" \
    -x "src/*" \
    -x "node_modules/*" \
    -x "package*.json" \
    -x "vite.config.js" \
    -x "svelte.config.js" \
    -x "test-location-service.html" \
    -x "dist/src/*"

echo "✅ Extension packed successfully as tavoos-extension-v$VERSION.zip"
echo "📁 Package size: $(du -h "releases/tavoos-extension-v$VERSION.zip" | cut -f1)"
echo "🚀 You can now upload this file to the Chrome Web Store Developer Dashboard."
