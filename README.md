# Tavoos Chrome Extension

Islamic companion Chrome extension with prayer times, <PERSON><PERSON> direction, and Quran verses with recitation.

## 🏗️ Architecture

The extension uses a simple, focused architecture:
- **New Tab Page**: Comprehensive Islamic dashboard with prayer times, Qibla compass, and Quran verses
- **Toolbar Icon**: Click to open a new tab with Tavoos content
- **Background Service**: Prayer time notifications and scheduling

## 🎯 Features

- **Prayer Times**: Accurate prayer times based on your location
- **Qibla Direction**: Visual compass pointing to Mecca
- **Quran Verses**: Daily verses with Arabic text and audio recitation
- **Prayer Notifications**: Automatic alerts at prayer times
- **Location Service**: Remote API for worldwide location data

## 🧪 Local Testing

### Method 1: Load Unpacked Extension (Recommended)

1. **Open Chrome and navigate to:**
   ```
   chrome://extensions/
   ```

2. **Enable Developer mode** (toggle in top-right corner)

3. **Click "Load unpacked"** and select the project root directory

4. **Test the extension:**
   - Click the Tavoos icon in the toolbar → Opens new tab with Tavoos content
   - Open a new tab manually → Shows Tavoos new tab page
   - Check notifications → Tests background prayer time alerts

### Method 2: Pack and Install

```bash
./_pack.sh
```

Then drag the generated `.zip` file from `releases/` folder to `chrome://extensions/`

## 📦 Production Deployment

### Build and Package

```bash
./_pack.sh
```

This script:
1. Creates a production-ready `.zip` file in `releases/`
2. Excludes development files and build artifacts
3. Includes only essential extension files

### Chrome Web Store Submission

1. **Go to:** [Chrome Web Store Developer Dashboard](https://chrome.google.com/webstore/devconsole/)

2. **Upload the package:** `releases/tavoos-extension-v{version}.zip`

3. **Required Information:**
   - **Single Purpose:** Islamic prayer companion with new tab replacement
   - **Permissions Justification:**
     - `storage`: Save user's location preferences and prayer settings
     - `alarms`: Schedule prayer time notifications
     - `notifications`: Display prayer time alerts
     - `tabs`: Open new tabs when toolbar icon is clicked

4. **Privacy Practices:**
   - Location data is stored locally and used only for prayer time calculations
   - No data is transmitted to external servers except for prayer time APIs
   - Audio files are served from trusted CDN (file-keeper.com)

## 🛠️ Development Commands

```bash
# Pack for production
./_pack.sh

# Test locally
# Load project folder as unpacked extension in Chrome
```

## 📁 Project Structure

```
├── newtab/               # New tab page (main interface)
│   ├── index.html        # HTML structure
│   ├── newtab.js         # Main JavaScript logic
│   ├── styles.css        # Styling
│   ├── location-service.js # Location API integration
│   ├── prayer-times.js   # Prayer time calculations
│   └── quran-service.js  # Quran verse API
├── qibla/                # Legacy Qibla popup (kept for reference)
├── fonts/                # Arabic fonts for Quran text
├── background.js         # Service worker (notifications, toolbar)
├── manifest.json         # Extension manifest
└── _pack.sh             # Production build script
```

## 🔧 Technical Details

### New Tab Features

- **Prayer Times:** Real-time prayer schedule based on location
- **Qibla Compass:** Visual compass with needle pointing to Mecca
- **Quran Integration:** Daily verses with Arabic text and audio
- **Location Service:** Remote API for worldwide location data
- **Chrome Storage:** Persistent user preferences

### API Integration

- **Tavoos Prayer API:** Coordinate-based prayer time calculations
- **Tavoos Quran API:** Verse retrieval with audio recitations
- **Location API:** Continent → Country → City hierarchy
- **Fallback Logic:** EU → BE → Gent default location

### Architecture

- **Vanilla JavaScript:** No build process required
- **Modular Design:** Separate services for different features
- **Chrome Extension APIs:** Storage, notifications, alarms

## 🐛 Troubleshooting

### Extension Loading Issues

1. Ensure all files are present in project directory
2. Check Chrome Developer Console for JavaScript errors
3. Verify manifest.json is valid JSON

### API Issues

- Location APIs may be slow on first load
- Check browser console for network errors
- Verify Chrome storage permissions
- Test with different locations if prayer times don't load

### Common Issues

- **New tab not showing:** Check if extension is enabled and new tab override is working
- **Prayer times missing:** Verify location is selected and API is accessible
- **Notifications not working:** Check Chrome notification permissions

## 📝 Version History

- **v3.0.0.0:** New tab-only implementation, removed popup
- **v2.2.0.0:** Utilize remote locations API
- **v2.1.x:** Vanilla JS implementation with local location data

## 🤝 Contributing

1. Make changes to JavaScript files in `newtab/`
2. Test locally by loading as unpacked extension
3. Run `./_pack.sh` to create distribution package
4. Submit pull request with description of changes
