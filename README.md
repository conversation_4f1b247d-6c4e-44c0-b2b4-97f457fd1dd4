# Tavoos Chrome Extension

Islamic companion Chrome extension with prayer times, <PERSON><PERSON> direction, and Quran verses with recitation.

## 🏗️ Architecture

The extension uses a hybrid architecture:
- **New Tab Page**: Vanilla JavaScript (comprehensive Islamic dashboard)
- **Popup (Qibla)**: Svelte (modern, reactive compass interface)
- **Background Service**: Vanilla JavaScript (prayer time notifications)

## 🚀 Development Setup

### Prerequisites

- Node.js v24.4.0 (managed via nvm)
- npm v11.4.2+

### Environment Setup

```bash
# Setup Node environment (required before any npm commands)
source ~/.zshrc && nvm use
```

### Install Dependencies

```bash
npm install
```

### Build the Svelte Popup

```bash
npm run build:popup
```

This command:
1. Builds the Svelte popup components
2. Copies required assets (images, icons)
3. Fixes asset paths for Chrome extension compatibility

## 🧪 Local Testing

### Method 1: Load Unpacked Extension (Recommended)

1. **Build the popup first:**
   ```bash
   source ~/.zshrc && nvm use
   npm run build:popup
   ```

2. **Open Chrome and navigate to:**
   ```
   chrome://extensions/
   ```

3. **Enable Developer mode** (toggle in top-right corner)

4. **Click "Load unpacked"** and select the project root directory

5. **Test the extension:**
   - Click the Tavoos icon in the toolbar → Tests Svelte popup
   - Open a new tab → Tests vanilla JS new tab page
   - Check notifications → Tests background service

### Method 2: Pack and Install

```bash
./pack.sh
```

Then drag the generated `.zip` file from `releases/` folder to `chrome://extensions/`

## 📦 Production Deployment

### Build and Package

```bash
./pack.sh
```

This script:
1. Sets up the Node environment
2. Builds the Svelte popup
3. Creates a production-ready `.zip` file in `releases/`
4. Excludes development files and source code

### Chrome Web Store Submission

1. **Go to:** [Chrome Web Store Developer Dashboard](https://chrome.google.com/webstore/devconsole/)

2. **Upload the package:** `releases/tavoos-extension-v{version}.zip`

3. **Required Information:**
   - **Single Purpose:** Islamic prayer companion with Qibla direction
   - **Permissions Justification:**
     - `storage`: Save user's location preferences and prayer settings
     - `alarms`: Schedule prayer time notifications
     - `notifications`: Display prayer time alerts
     - `geolocation`: Auto-detect user location for accurate prayer times
     - `activeTab`: Access current tab for enhanced Qibla features

4. **Privacy Practices:**
   - Location data is stored locally and used only for prayer time calculations
   - No data is transmitted to external servers except for prayer time APIs
   - Audio files are served from trusted CDN (file-keeper.com)

## 🛠️ Development Commands

```bash
# Build Svelte popup
npm run build:popup

# Development server (for popup only)
npm run dev

# Copy assets only
npm run copy-assets

# Fix asset paths
npm run fix-paths

# Pack for production
./pack.sh
```

## 📁 Project Structure

```
├── src/                    # Svelte source code
│   ├── popup/             # Popup entry point
│   └── lib/               # Shared Svelte components
│       ├── components/    # UI components
│       ├── services/      # API services
│       └── stores/        # State management
├── dist/                  # Built Svelte files
├── newtab/               # Vanilla JS new tab page
├── qibla/                # Legacy popup (replaced by Svelte)
├── background.js         # Service worker
├── manifest.json         # Extension manifest
└── pack.sh              # Production build script
```

## 🔧 Technical Details

### Svelte Popup Features

- **Reactive Location Selection:** Continent → Country → City dropdowns
- **Real-time Qibla Compass:** Visual compass with needle pointing to Mecca
- **State Management:** Svelte stores for location data
- **Chrome Storage Integration:** Persistent location preferences
- **Error Handling:** Graceful fallbacks for API failures

### API Integration

- **Location Data:** Remote Tavoos API with local caching
- **Prayer Times:** Coordinate-based calculations
- **Fallback Logic:** EU → BE → Gent default location

### Build Process

- **Vite:** Modern build tool with Svelte plugin
- **Asset Optimization:** Automatic image and CSS optimization
- **Chrome Extension Compatibility:** CSP-compliant builds

## 🐛 Troubleshooting

### Build Issues

```bash
# Clear node_modules and reinstall
rm -rf node_modules package-lock.json
npm install

# Ensure correct Node version
source ~/.zshrc && nvm use
```

### Extension Loading Issues

1. Check that `dist/popup.html` exists after building
2. Verify manifest.json points to correct popup file
3. Ensure all assets are copied to dist/ folder

### API Issues

- Location APIs may be slow on first load
- Check browser console for network errors
- Verify Chrome storage permissions

## 📝 Version History

- **v3.0.0.0:** Reimplement in Svelte
- **v2.2.0.0:** Utilize remote locations API
- **v2.1.x:** Vanilla JS implementation with local location data

## 🤝 Contributing

1. Follow the development setup above
2. Make changes to Svelte components in `src/`
3. Test locally with `npm run build:popup`
4. Submit pull request with description of changes
