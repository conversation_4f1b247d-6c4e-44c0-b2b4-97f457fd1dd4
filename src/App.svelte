<script>
  import { onMount } from 'svelte';
  import Header from './lib/components/Header.svelte';
  import PrayerTimes from './lib/components/PrayerTimes.svelte';
  import QiblaCompass from './lib/components/QiblaCompass.svelte';
  import QuranVerse from './lib/components/QuranVerse.svelte';
  import LocationSelector from './lib/components/LocationSelector.svelte';
  import Footer from './lib/components/Footer.svelte';

  // Services
  import { locationService } from './lib/services/locationService.js';
  import { prayerService } from './lib/services/prayerService.js';
  import { quranService } from './lib/services/quranService.js';

  // Simple state variables - no reactive declarations
  let isLoading = true;
  let currentLocation = { continent: null, country: null, city: null };
  let prayerTimes = null;
  let currentVerse = null;
  let error = null;

  // Initialize the app
  onMount(async () => {
    console.log('Tavoos app initializing...');

    try {
      isLoading = true;
      error = null;

      // Initialize location service
      console.log('Initializing location service...');
      await locationService.initialize();

      // Get stored location or set default
      console.log('Getting stored location...');
      const storedLocation = await locationService.getStoredLocation();

      if (!storedLocation.city) {
        console.log('No stored location, setting default...');
        await setDefaultLocation();
      } else {
        console.log('Using stored location:', storedLocation);
        currentLocation = storedLocation;
      }

      // Load prayer times and Quran verse
      console.log('Loading prayer times and Quran verse...');
      await loadPrayerTimes();
      await loadQuranVerse();

      console.log('App initialization complete');

    } catch (err) {
      console.error('Failed to initialize app:', err);
      error = 'Failed to load Islamic content. Please refresh the page.';
    } finally {
      isLoading = false;
    }
  });

  async function setDefaultLocation() {
    try {
      console.log('Loading continents...');
      const continents = await locationService.getContinents();
      const europe = continents.find(c => c.code === 'EU') || continents[0];

      if (!europe) {
        throw new Error('No continents available');
      }

      console.log('Loading countries for', europe.name);
      const countries = await locationService.getCountries(europe.id);
      const belgium = countries.find(c => c.code === 'BE') || countries[0];

      if (!belgium) {
        throw new Error('No countries available');
      }

      console.log('Loading cities for', belgium.name);
      const cities = await locationService.getCities(belgium.id);
      const gent = cities.find(c => c.name === 'Gent') || cities[0];

      if (!gent) {
        throw new Error('No cities available');
      }

      currentLocation = {
        continent: europe,
        country: belgium,
        city: gent
      };

      console.log('Default location set:', currentLocation);
      await locationService.storeLocation(europe, belgium, gent);
    } catch (err) {
      console.error('Failed to set default location:', err);
      throw err; // Re-throw to be caught by main error handler
    }
  }

  async function loadPrayerTimes() {
    if (!currentLocation?.city) {
      console.log('No city selected, skipping prayer times');
      return;
    }

    try {
      console.log('Loading prayer times for', currentLocation.city.name);
      prayerTimes = await prayerService.getTodaysPrayerTimes(
        currentLocation.city.latitude,
        currentLocation.city.longitude
      );
      console.log('Prayer times loaded:', prayerTimes);
    } catch (err) {
      console.error('Failed to load prayer times:', err);
      // Don't throw, just log the error
    }
  }

  async function loadQuranVerse() {
    try {
      console.log('Loading Quran verse...');
      currentVerse = await quranService.getRandomVerse();
      console.log('Quran verse loaded:', currentVerse);
    } catch (err) {
      console.error('Failed to load Quran verse:', err);
      // Don't throw, just log the error
    }
  }

  // Handle location change
  async function handleLocationChange(newLocation) {
    currentLocation = newLocation;
    await loadPrayerTimes();
  }

  // Handle verse refresh
  async function handleVerseRefresh() {
    await loadQuranVerse();
  }
</script>

<main class="app">
  {#if isLoading}
    <div class="loading-container">
      <div class="spinner"></div>
      <p>Loading your spiritual moments...</p>
    </div>
  {:else if error}
    <div class="error-container">
      <h2>⚠️ Something went wrong</h2>
      <p>{error}</p>
      <button on:click={() => window.location.reload()}>
        Try Again
      </button>
    </div>
  {:else}
    <Header />

    <div class="content">
      <div class="main-grid">
        <!-- Left Column: Prayer Times & Location -->
        <div class="left-column">
          <LocationSelector
            {currentLocation}
            onLocationChange={handleLocationChange}
          />

          {#if prayerTimes}
            <PrayerTimes
              {prayerTimes}
              cityName={currentLocation?.city?.name}
            />
          {/if}
        </div>

        <!-- Center Column: Qibla Compass -->
        <div class="center-column">
          {#if currentLocation?.city}
            <QiblaCompass
              city={currentLocation.city}
              cityName={currentLocation.city.name}
              countryName={currentLocation.country?.name}
            />
          {/if}
        </div>

        <!-- Right Column: Quran Verse -->
        <div class="right-column">
          {#if currentVerse}
            <QuranVerse
              verse={currentVerse}
              onRefresh={handleVerseRefresh}
            />
          {/if}
        </div>
      </div>
    </div>

    <Footer />
  {/if}
</main>

<style>
  .app {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    gap: 1rem;
  }

  .error-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    gap: 1rem;
    text-align: center;
    padding: 2rem;
  }

  .error-container button {
    background: var(--button-primary-bg);
    color: var(--button-primary-text);
    border: 1px solid var(--button-secondary-border);
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.2s ease;
  }

  .error-container button:hover {
    background: white;
    transform: translateY(-1px);
  }

  .content {
    flex: 1;
    padding: 2rem;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
  }

  .main-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 2rem;
    min-height: 70vh;
  }

  .left-column,
  .center-column,
  .right-column {
    display: flex;
    flex-direction: column;
    gap: 2rem;
  }

  .center-column {
    align-items: center;
    justify-content: center;
  }

  /* Responsive Design */
  @media (max-width: 1200px) {
    .main-grid {
      grid-template-columns: 1fr 1fr;
      grid-template-rows: auto auto;
    }

    .center-column {
      grid-column: 1 / -1;
      order: -1;
    }
  }

  @media (max-width: 768px) {
    .content {
      padding: 1rem;
    }

    .main-grid {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }

    .center-column {
      order: 0;
    }
  }

  .spinner {
    width: 50px;
    height: 50px;
    border: 4px solid var(--bg-overlay);
    border-top: 4px solid var(--tavoos-light);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
</style>
