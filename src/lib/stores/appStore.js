import { writable, derived } from 'svelte/store';

// App state stores
export const isLoading = writable(true);
export const error = writable(null);

// Location state - this is the main reactive state
export const location = writable({
  continent: null,
  country: null,
  city: null
});

// Available data stores
export const availableData = writable({
  continents: [],
  countries: [],
  cities: []
});

// Derived stores - these automatically update when dependencies change
export const hasCompleteLocation = derived(
  location,
  ($location) => !!(
    $location.continent && 
    $location.country && 
    $location.city
  )
);

export const qiblaData = derived(
  [location, hasCompleteLocation],
  ([$location, $hasLocation]) => {
    if (!$hasLocation || !$location.city) return null;
    
    const { latitude, longitude } = $location.city;
    if (!latitude || !longitude) return null;

    // Calculate Qibla direction - reactive calculation!
    const toRadians = deg => deg * Math.PI / 180;
    const toDegrees = rad => rad * 180 / Math.PI;

    const startLat = toRadians(latitude);
    const startLng = toRadians(longitude);

    // Coordinates of Mecca
    const destLat = toRadians(21.3891);
    const destLng = toRadians(39.8579);

    const y = Math.sin(destLng - startLng) * Math.cos(destLat);
    const x = Math.cos(startLat) * Math.sin(destLat) - 
              Math.sin(startLat) * Math.cos(destLat) * Math.cos(destLng - startLng);
    
    const bearing = toDegrees(Math.atan2(y, x)) + 180;
    const degrees = Math.floor(bearing);
    const minutes = Math.floor(60 * (bearing - degrees));

    return {
      bearing,
      degrees,
      minutes,
      city: $location.city,
      coordinates: { latitude, longitude }
    };
  }
);

// Actions - these are reactive functions that update stores
export const actions = {
  setLoading: (loading) => isLoading.set(loading),
  
  setError: (err) => error.set(err),
  
  clearError: () => error.set(null),
  
  updateLocation: (newLocation) => {
    location.update(current => ({ ...current, ...newLocation }));
  },
  
  setAvailableData: (type, data) => {
    availableData.update(current => ({
      ...current,
      [type]: data
    }));
  },
  
  selectContinent: (continent) => {
    location.update(current => ({
      ...current,
      continent,
      country: null,
      city: null
    }));
  },
  
  selectCountry: (country) => {
    location.update(current => ({
      ...current,
      country,
      city: null
    }));
  },
  
  selectCity: (city) => {
    location.update(current => ({
      ...current,
      city
    }));
  }
};
