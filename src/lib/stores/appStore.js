// Svelte 5 runes-based stores
import { writable } from 'svelte/store';

// Simple writable stores for Svelte 5 compatibility
export const isLoading = writable(true);
export const error = writable(null);

// Location state
export const location = writable({
  continent: null,
  country: null,
  city: null
});

// Available data
export const availableData = writable({
  continents: [],
  countries: [],
  cities: []
});

// Computed qibla data - we'll calculate this in components
export const qiblaData = writable(null);

// Actions - these are reactive functions that update stores
export const actions = {
  setLoading: (loading) => isLoading.set(loading),

  setError: (err) => error.set(err),

  clearError: () => error.set(null),

  updateLocation: (newLocation) => {
    location.update(current => ({ ...current, ...newLocation }));
  },

  setAvailableData: (type, data) => {
    availableData.update(current => ({
      ...current,
      [type]: data
    }));
  },

  selectContinent: (continent) => {
    location.update(current => ({
      ...current,
      continent,
      country: null,
      city: null
    }));
  },

  selectCountry: (country) => {
    location.update(current => ({
      ...current,
      country,
      city: null
    }));
  },

  selectCity: (city) => {
    location.update(current => ({
      ...current,
      city
    }));
  }
};
