import { writable, derived } from 'svelte/store';

// Core location data stores
export const continents = writable([]);
export const countries = writable([]);
export const cities = writable([]);

// Current selection stores
export const selectedContinent = writable(null);
export const selectedCountry = writable(null);
export const selectedCity = writable(null);

// Derived store for complete location - this is reactive Svelte magic!
export const currentLocation = derived(
  [selectedContinent, selectedCountry, selectedCity],
  ([$continent, $country, $city]) => ({
    continent: $continent,
    country: $country,
    city: $city,
    isComplete: !!(($continent && $country && $city))
  })
);

// Derived store for location display text
export const locationText = derived(
  currentLocation,
  ($location) => {
    if (!$location.isComplete) return 'No location selected';
    return `${$location.city.name}, ${$location.country.name}`;
  }
);

// Store for loading states
export const loadingStates = writable({
  continents: false,
  countries: false,
  cities: false,
  initializing: true
});

// Derived store for any loading state
export const isAnyLoading = derived(
  loadingStates,
  ($states) => Object.values($states).some(Boolean)
);
