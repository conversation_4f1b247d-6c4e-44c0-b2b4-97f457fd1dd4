<script>
  export let message = 'Loading...';
  export let size = 'medium'; // small, medium, large
</script>

<div class="loading-container">
  <div class="spinner" class:small={size === 'small'} class:large={size === 'large'}></div>
  <p class="message">{message}</p>
</div>

<style>
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    padding: 2rem;
  }

  .spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--bg-overlay);
    border-top: 3px solid var(--tavoos-light);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .spinner.small {
    width: 24px;
    height: 24px;
    border-width: 2px;
  }

  .spinner.large {
    width: 60px;
    height: 60px;
    border-width: 4px;
  }

  .message {
    color: var(--text-secondary);
    font-size: 0.9rem;
    text-align: center;
    margin: 0;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
</style>
