<script>
  import { onMount, onDestroy } from 'svelte';
  import { prayerService } from '../services/prayerService.js';
  
  export let prayerTimes;
  export let cityName = '';
  
  let timeUntilNext = null;
  let interval;

  function updateCountdown() {
    if (!prayerTimes?.nextPrayer) return;
    
    const now = new Date();
    const currentMinutes = now.getHours() * 60 + now.getMinutes();
    
    timeUntilNext = prayerService.prototype.calculateTimeUntilNext(
      prayerTimes.nextPrayer, 
      currentMinutes
    );
  }

  onMount(() => {
    updateCountdown();
    interval = setInterval(updateCountdown, 60000); // Update every minute
  });

  onDestroy(() => {
    if (interval) clearInterval(interval);
  });

  function formatTimeUntil(timeObj) {
    if (!timeObj) return '';
    
    if (timeObj.hours > 0) {
      return `${timeObj.hours}h ${timeObj.minutes}m`;
    } else {
      return `${timeObj.minutes}m`;
    }
  }

  function isPrayerCurrent(prayerKey) {
    return prayerTimes?.currentPrayer?.key === prayerKey;
  }

  function isPrayerNext(prayerKey) {
    return prayerTimes?.nextPrayer?.key === prayerKey;
  }
</script>

<div class="prayer-times card">
  <h3 class="section-title">🕌 Prayer Times</h3>
  
  {#if cityName}
    <div class="location-info">
      {cityName}
    </div>
  {/if}

  {#if prayerTimes?.nextPrayer && timeUntilNext}
    <div class="next-prayer">
      <div class="next-prayer-name">
        Next: {prayerTimes.nextPrayer.name}
      </div>
      <div class="countdown">
        {formatTimeUntil(timeUntilNext)}
        {#if prayerTimes.nextPrayer.isTomorrow}
          <span class="tomorrow">(tomorrow)</span>
        {/if}
      </div>
    </div>
  {/if}

  <div class="prayers-list">
    {#each Object.entries(prayerTimes?.prayers || {}) as [key, prayer] (key)}
      <div 
        class="prayer-item"
        class:current={isPrayerCurrent(key)}
        class:next={isPrayerNext(key)}
        class:passed={prayer.isPassed}
      >
        <div class="prayer-name">
          {prayer.name}
          {#if isPrayerCurrent(key)}
            <span class="status-badge current-badge">Current</span>
          {:else if isPrayerNext(key)}
            <span class="status-badge next-badge">Next</span>
          {/if}
        </div>
        <div class="prayer-time">
          {prayerService.formatTime(prayer.time)}
        </div>
      </div>
    {/each}
  </div>
</div>

<style>
  .prayer-times {
    animation: slideIn 0.6s ease-out;
  }

  .section-title {
    font-size: 1.2rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .location-info {
    text-align: center;
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 1rem;
    font-weight: 500;
  }

  .next-prayer {
    background: var(--highlight-bg);
    border-radius: 0.75rem;
    padding: 1rem;
    margin-bottom: 1.5rem;
    text-align: center;
    border: 1px solid var(--tavoos-light);
  }

  .next-prayer-name {
    font-size: 1rem;
    font-weight: 600;
    color: var(--tavoos-light);
    margin-bottom: 0.5rem;
  }

  .countdown {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  }

  .tomorrow {
    font-size: 0.8rem;
    color: var(--text-secondary);
    font-weight: 400;
  }

  .prayers-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .prayer-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    border: 1px solid transparent;
  }

  .prayer-item:hover {
    background: var(--bg-overlay);
    transform: translateX(4px);
  }

  .prayer-item.current {
    background: rgba(0, 200, 180, 0.2);
    border-color: var(--tavoos-light);
  }

  .prayer-item.next {
    background: rgba(255, 140, 0, 0.2);
    border-color: var(--button-primary-bg);
  }

  .prayer-item.passed {
    opacity: 0.6;
  }

  .prayer-name {
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .prayer-time {
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    font-weight: 600;
    color: var(--text-secondary);
  }

  .status-badge {
    font-size: 0.7rem;
    padding: 0.2rem 0.5rem;
    border-radius: 1rem;
    font-weight: 500;
  }

  .current-badge {
    background: var(--tavoos-light);
    color: var(--tavoos-darker);
  }

  .next-badge {
    background: var(--button-primary-bg);
    color: var(--button-primary-text);
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .prayer-item {
      padding: 0.6rem;
    }
    
    .countdown {
      font-size: 1.25rem;
    }
    
    .section-title {
      font-size: 1.1rem;
    }
  }
</style>
