<script>
  import { actions } from '../stores/appStore.js';

  export let error;

  function retry() {
    actions.clearError();
    // Could trigger a retry action here
    window.location.reload();
  }
</script>

<div class="error-container">
  <div class="error-icon">⚠️</div>
  <h3 class="error-title">Something went wrong</h3>
  <p class="error-message">{error}</p>
  <button class="retry-button" on:click={retry}>
    Try Again
  </button>
</div>

<style>
  .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    padding: 2rem;
    text-align: center;
  }

  .error-icon {
    font-size: 2rem;
  }

  .error-title {
    color: var(--text-primary);
    font-size: 1.1rem;
    margin: 0;
  }

  .error-message {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin: 0;
    max-width: 200px;
  }

  .retry-button {
    background: var(--tavoos-primary);
    color: var(--text-primary);
    border: 1px solid var(--border-medium);
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.2s ease;
  }

  .retry-button:hover {
    background: var(--tavoos-light);
    transform: translateY(-1px);
  }

  .retry-button:active {
    transform: translateY(0);
  }
</style>
