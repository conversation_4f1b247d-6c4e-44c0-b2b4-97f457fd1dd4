<script>
  import { onMount } from 'svelte';
  
  let currentTime = '';
  let currentDate = '';

  function updateDateTime() {
    const now = new Date();
    
    // Format time
    currentTime = now.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: true
    });
    
    // Format date
    currentDate = now.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  onMount(() => {
    updateDateTime();
    const interval = setInterval(updateDateTime, 1000);
    
    return () => clearInterval(interval);
  });
</script>

<header class="header">
  <div class="header-content">
    <div class="logo-section">
      <a href="https://tavoos.eu" class="logo-link">
        <img src="./android-chrome-192x192.png" alt="Tavoos Logo" class="logo" />
        <h1 class="title">Tavoos</h1>
      </a>
    </div>
    
    <div class="datetime-section">
      <div class="time">{currentTime}</div>
      <div class="date">{currentDate}</div>
    </div>
  </div>
</header>

<style>
  .header {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--border-light);
    background: var(--bg-overlay);
    backdrop-filter: blur(10px);
  }

  .header-content {
    max-width: 1400px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .logo-section {
    display: flex;
    align-items: center;
  }

  .logo-link {
    display: flex;
    align-items: center;
    gap: 1rem;
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
  }

  .logo-link:hover {
    transform: translateY(-2px);
  }

  .logo {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    transition: all 0.3s ease;
  }

  .logo:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(0, 200, 180, 0.3);
  }

  .title {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    background: linear-gradient(135deg, var(--text-primary) 0%, var(--tavoos-light) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .datetime-section {
    text-align: right;
  }

  .time {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  }

  .date {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .header {
      padding: 1rem;
    }
    
    .header-content {
      flex-direction: column;
      gap: 1rem;
      text-align: center;
    }
    
    .datetime-section {
      text-align: center;
    }
    
    .title {
      font-size: 1.5rem;
    }
    
    .time {
      font-size: 1.2rem;
    }
    
    .logo {
      width: 40px;
      height: 40px;
    }
  }

  @media (max-width: 480px) {
    .logo-link {
      flex-direction: column;
      gap: 0.5rem;
    }
    
    .title {
      font-size: 1.25rem;
    }
  }
</style>
