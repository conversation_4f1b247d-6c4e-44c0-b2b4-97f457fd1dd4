<script>
  import { location, availableData } from '../stores/appStore.js';
  import { selectContinent, selectCountry, selectCity } from '../services/appService.js';

  // Reactive declarations - this is pure Svelte magic!
  $: continents = $availableData.continents;
  $: countries = $availableData.countries;
  $: cities = $availableData.cities;

  // Reactive values for selected items
  $: selectedContinentId = $location.continent?.id || '';
  $: selectedCountryId = $location.country?.id || '';
  $: selectedCityId = $location.city?.id || '';

  // Event handlers with reactive updates
  async function handleContinentChange(event) {
    const continentId = event.target.value;
    const continent = continents.find(c => c.id == continentId) || null;
    await selectContinent(continent);
  }

  async function handleCountryChange(event) {
    const countryId = event.target.value;
    const country = countries.find(c => c.id == countryId) || null;
    await selectCountry(country);
  }

  async function handleCityChange(event) {
    const cityId = event.target.value;
    const city = cities.find(c => c.id == cityId) || null;
    await selectCity(city);
  }
</script>

<div class="location-selector">
  <div class="select-group">
    <label for="continent-select">Continent</label>
    <select
      id="continent-select"
      value={selectedContinentId}
      on:change={handleContinentChange}
      class:has-value={selectedContinentId}
    >
      <option value="">Choose continent...</option>
      {#each continents as continent (continent.id)}
        <option value={continent.id}>{continent.name}</option>
      {/each}
    </select>
  </div>

  <div class="select-group">
    <label for="country-select">Country</label>
    <select
      id="country-select"
      value={selectedCountryId}
      on:change={handleCountryChange}
      disabled={!selectedContinentId}
      class:has-value={selectedCountryId}
    >
      <option value="">Choose country...</option>
      {#each countries as country (country.id)}
        <option value={country.id}>{country.name}</option>
      {/each}
    </select>
  </div>

  <div class="select-group">
    <label for="city-select">City</label>
    <select
      id="city-select"
      value={selectedCityId}
      on:change={handleCityChange}
      disabled={!selectedCountryId}
      class:has-value={selectedCityId}
    >
      <option value="">Choose city...</option>
      {#each cities as city (city.id)}
        <option value={city.id}>{city.name}</option>
      {/each}
    </select>
  </div>
</div>

<style>
  .location-selector {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    width: 100%;
    max-width: 240px;
  }

  .select-group {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }

  label {
    font-size: 0.8rem;
    color: var(--text-secondary);
    font-weight: 500;
  }

  select {
    background: var(--bg-overlay);
    padding: 0.6rem;
    color: var(--text-primary);
    border: 1px solid var(--border-medium);
    border-radius: 0.5rem;
    font-size: 0.9rem;
    transition: all 0.2s ease;
    cursor: pointer;
  }

  select:focus {
    outline: none;
    border-color: var(--tavoos-light);
    box-shadow: 0 0 0 2px rgba(0, 200, 180, 0.2);
  }

  select:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  select.has-value {
    border-color: var(--tavoos-light);
    background: rgba(0, 200, 180, 0.1);
  }

  option {
    background: var(--tavoos-primary);
    color: var(--text-primary);
    padding: 0.5rem;
  }

  /* Responsive design */
  @media (min-width: 300px) {
    .location-selector {
      flex-direction: row;
      max-width: none;
    }

    .select-group {
      flex: 1;
    }
  }
</style>
