<script>
  import { locationService } from '../services/locationService.js';

  export let currentLocation;
  export let onLocationChange = () => {};

  let continents = [];
  let countries = [];
  let cities = [];
  let isLoading = false;
  let error = null;

  // Reactive values for dropdowns
  $: selectedContinentId = currentLocation?.continent?.id || '';
  $: selectedCountryId = currentLocation?.country?.id || '';
  $: selectedCityId = currentLocation?.city?.id || '';

  // Load initial data
  async function loadContinents() {
    try {
      isLoading = true;
      continents = await locationService.getContinents();

      // If we have a current location, load its countries and cities
      if (currentLocation?.continent) {
        await loadCountries(currentLocation.continent.id);
        if (currentLocation?.country) {
          await loadCities(currentLocation.country.id);
        }
      }
    } catch (err) {
      error = 'Failed to load location data';
      console.error('Error loading continents:', err);
    } finally {
      isLoading = false;
    }
  }

  async function loadCountries(continentId) {
    try {
      countries = await locationService.getCountries(continentId);
    } catch (err) {
      error = 'Failed to load countries';
      console.error('Error loading countries:', err);
    }
  }

  async function loadCities(countryId) {
    try {
      cities = await locationService.getCities(countryId);
    } catch (err) {
      error = 'Failed to load cities';
      console.error('Error loading cities:', err);
    }
  }

  async function handleContinentChange(event) {
    const continentId = event.target.value;

    if (!continentId) {
      countries = [];
      cities = [];
      return;
    }

    const continent = continents.find(c => c.id == continentId);

    // Reset dependent selections
    countries = [];
    cities = [];

    // Update location
    const newLocation = {
      continent,
      country: null,
      city: null
    };

    onLocationChange(newLocation);

    // Load countries
    await loadCountries(continentId);
  }

  async function handleCountryChange(event) {
    const countryId = event.target.value;

    if (!countryId) {
      cities = [];
      return;
    }

    const country = countries.find(c => c.id == countryId);

    // Reset dependent selections
    cities = [];

    // Update location
    const newLocation = {
      ...currentLocation,
      country,
      city: null
    };

    onLocationChange(newLocation);

    // Load cities
    await loadCities(countryId);
  }

  async function handleCityChange(event) {
    const cityId = event.target.value;

    if (!cityId) return;

    const city = cities.find(c => c.id == cityId);

    // Update location
    const newLocation = {
      ...currentLocation,
      city
    };

    onLocationChange(newLocation);

    // Store the complete location
    try {
      await locationService.storeLocation(
        newLocation.continent,
        newLocation.country,
        city
      );
    } catch (err) {
      console.error('Failed to store location:', err);
    }
  }

  // Initialize on mount
  loadContinents();
</script>

<div class="location-selector card">
  <h3 class="section-title">📍 Your Location</h3>

  {#if error}
    <div class="error-message">
      ⚠️ {error}
      <button class="retry-btn" on:click={loadContinents}>
        Retry
      </button>
    </div>
  {/if}

  <div class="selectors">
    <div class="selector-group">
      <label for="continent">Continent</label>
      <select
        id="continent"
        value={selectedContinentId}
        on:change={handleContinentChange}
        disabled={isLoading}
      >
        <option value="">Choose continent...</option>
        {#each continents as continent (continent.id)}
          <option value={continent.id}>{continent.name}</option>
        {/each}
      </select>
    </div>

    <div class="selector-group">
      <label for="country">Country</label>
      <select
        id="country"
        value={selectedCountryId}
        on:change={handleCountryChange}
        disabled={!selectedContinentId || isLoading}
      >
        <option value="">Choose country...</option>
        {#each countries as country (country.id)}
          <option value={country.id}>{country.name}</option>
        {/each}
      </select>
    </div>

    <div class="selector-group">
      <label for="city">City</label>
      <select
        id="city"
        value={selectedCityId}
        on:change={handleCityChange}
        disabled={!selectedCountryId || isLoading}
      >
        <option value="">Choose city...</option>
        {#each cities as city (city.id)}
          <option value={city.id}>{city.name}</option>
        {/each}
      </select>
    </div>
  </div>

  {#if currentLocation?.city}
    <div class="current-location">
      <strong>📍 {currentLocation.city.name}, {currentLocation.country?.name}</strong>
    </div>
  {/if}
</div>

<style>
  .location-selector {
    animation: fadeIn 0.6s ease-out;
  }

  .section-title {
    font-size: 1.2rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .error-message {
    background: rgba(255, 0, 0, 0.1);
    border: 1px solid rgba(255, 0, 0, 0.3);
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .retry-btn {
    background: var(--button-primary-bg);
    color: var(--button-primary-text);
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    cursor: pointer;
    font-size: 0.8rem;
  }

  .selectors {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .selector-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  label {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-secondary);
  }

  select {
    width: 100%;
    transition: all 0.2s ease;
  }

  select:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  select:focus {
    transform: translateY(-1px);
  }

  .current-location {
    margin-top: 1rem;
    padding: 0.75rem;
    background: var(--highlight-bg);
    border-radius: 0.5rem;
    text-align: center;
    color: var(--tavoos-light);
    font-size: 0.9rem;
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .selectors {
      gap: 0.75rem;
    }

    .section-title {
      font-size: 1.1rem;
    }
  }
</style>
