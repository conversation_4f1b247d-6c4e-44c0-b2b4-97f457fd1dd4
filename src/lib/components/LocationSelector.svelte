<script>
  import { onMount } from 'svelte';
  import { locationStore, continentsStore, countriesStore, citiesStore } from '../stores/locationStore.js';
  import { locationService } from '../services/locationService.js';

  let continents = [];
  let countries = [];
  let cities = [];
  let currentLocation = { continent: null, country: null, city: null };

  // Subscribe to stores
  continentsStore.subscribe(value => continents = value);
  countriesStore.subscribe(value => countries = value);
  citiesStore.subscribe(value => cities = value);
  locationStore.subscribe(value => currentLocation = value);

  onMount(async () => {
    // Load continents
    const continentsData = await locationService.getContinents();
    continentsStore.set(continentsData);

    // If we have a stored location, populate the dropdowns
    if (currentLocation.continent) {
      await loadCountries(currentLocation.continent.id);
      if (currentLocation.country) {
        await loadCities(currentLocation.country.id);
      }
    }
  });

  async function onContinentChange(event) {
    const continentId = event.target.value;
    if (!continentId) {
      countriesStore.set([]);
      citiesStore.set([]);
      return;
    }

    const continent = continents.find(c => c.id == continentId);
    await loadCountries(continentId);
    
    // Update location store
    locationStore.update(loc => ({
      ...loc,
      continent,
      country: null,
      city: null
    }));
  }

  async function onCountryChange(event) {
    const countryId = event.target.value;
    if (!countryId) {
      citiesStore.set([]);
      return;
    }

    const country = countries.find(c => c.id == countryId);
    await loadCities(countryId);
    
    // Update location store
    locationStore.update(loc => ({
      ...loc,
      country,
      city: null
    }));
  }

  async function onCityChange(event) {
    const cityId = event.target.value;
    if (!cityId) return;

    const city = cities.find(c => c.id == cityId);
    
    // Update location store and save to storage
    locationStore.update(loc => {
      const newLocation = { ...loc, city };
      locationService.storeLocation(newLocation.continent, newLocation.country, city);
      return newLocation;
    });
  }

  async function loadCountries(continentId) {
    const countriesData = await locationService.getCountries(continentId);
    countriesStore.set(countriesData);
  }

  async function loadCities(countryId) {
    const citiesData = await locationService.getCities(countryId);
    citiesStore.set(citiesData);
  }
</script>

<div class="location-selector">
  <select 
    value={currentLocation.continent?.id || ''} 
    on:change={onContinentChange}
  >
    <option value="">Continent</option>
    {#each continents as continent}
      <option value={continent.id}>{continent.name}</option>
    {/each}
  </select>

  <select 
    value={currentLocation.country?.id || ''} 
    on:change={onCountryChange}
  >
    <option value="">Country</option>
    {#each countries as country}
      <option value={country.id}>{country.name}</option>
    {/each}
  </select>

  <select 
    value={currentLocation.city?.id || ''} 
    on:change={onCityChange}
  >
    <option value="">City</option>
    {#each cities as city}
      <option value={city.id}>{city.name}</option>
    {/each}
  </select>
</div>

<style>
  .location-selector {
    display: flex;
    gap: 0.5rem;
    margin: 1rem 0;
    width: 100%;
  }

  select {
    background: var(--bg-overlay);
    padding: 0.5rem;
    color: var(--text-primary);
    border: thin solid var(--border-medium);
    border-radius: 0.5rem;
    flex: 1;
    font-size: 0.9rem;
  }

  select:focus {
    outline: none;
    border-color: var(--tavoos-light);
  }

  option {
    background: var(--tavoos-primary);
    color: var(--text-primary);
  }
</style>
