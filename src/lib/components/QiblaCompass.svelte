<script>
  import { onMount } from 'svelte';
  import { locationService } from '../services/locationService.js';
  
  export let city;
  export let cityName = '';
  export let countryName = '';
  
  let compassElement;
  let qiblaDirection = 0;
  let degrees = 0;
  let minutes = 0;

  $: if (city && city.latitude && city.longitude) {
    calculateQibla();
  }

  function calculateQibla() {
    if (!city?.latitude || !city?.longitude) return;
    
    qiblaDirection = locationService.calculateQiblaDirection(
      city.latitude, 
      city.longitude
    );
    
    degrees = Math.floor(qiblaDirection);
    minutes = Math.floor(60 * (qiblaDirection - degrees));
    
    updateCompassNeedle();
  }

  function updateCompassNeedle() {
    if (!compassElement) return;
    
    // Clear existing needle
    const existingNeedle = compassElement.querySelector('.needle');
    if (existingNeedle) {
      existingNeedle.remove();
    }
    
    // Create new needle
    const needle = document.createElement('div');
    needle.classList.add('needle');
    needle.style.transform = `rotate(${qiblaDirection}deg)`;
    
    compassElement.appendChild(needle);
  }

  onMount(() => {
    if (city) {
      calculateQibla();
    }
  });
</script>

<div class="qibla-compass card">
  <h3 class="section-title">🧭 Qibla Direction</h3>
  
  {#if cityName}
    <div class="location-info">
      From {cityName}{countryName ? `, ${countryName}` : ''}
    </div>
  {/if}

  <div class="compass-container">
    <div 
      bind:this={compassElement}
      class="compass"
    >
      <div class="compass-rose">
        <div class="direction north">N</div>
        <div class="direction east">E</div>
        <div class="direction south">S</div>
        <div class="direction west">W</div>
      </div>
      <div class="center-dot"></div>
    </div>
  </div>

  <div class="qibla-info">
    <div class="bearing">
      <span class="degrees-value">{degrees}</span>°
      <span class="minutes-value">{minutes}</span>'
    </div>
    <div class="direction-label">towards Mecca</div>
  </div>

  <div class="mecca-info">
    <div class="kaaba-icon">🕋</div>
    <div class="mecca-text">
      <div class="mecca-name">Masjid al-Haram</div>
      <div class="mecca-location">Mecca, Saudi Arabia</div>
    </div>
  </div>
</div>

<style>
  .qibla-compass {
    text-align: center;
    animation: fadeIn 0.8s ease-out;
  }

  .section-title {
    font-size: 1.2rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
  }

  .location-info {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
    font-weight: 500;
  }

  .compass-container {
    display: flex;
    justify-content: center;
    margin: 2rem 0;
  }

  .compass {
    width: 200px;
    height: 200px;
    border: 3px solid var(--border-medium);
    border-radius: 50%;
    position: relative;
    background: radial-gradient(circle, var(--bg-overlay) 0%, transparent 70%);
    transition: all 0.3s ease;
  }

  .compass:hover {
    transform: scale(1.05);
    border-color: var(--tavoos-light);
    box-shadow: 0 0 20px rgba(0, 200, 180, 0.3);
  }

  .compass-rose {
    width: 100%;
    height: 100%;
    position: relative;
  }

  .direction {
    position: absolute;
    font-weight: 700;
    font-size: 1.2rem;
    color: var(--text-primary);
  }

  .direction.north {
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    color: var(--tavoos-light);
  }

  .direction.east {
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
  }

  .direction.south {
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
  }

  .direction.west {
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
  }

  .center-dot {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 8px;
    height: 8px;
    background: var(--tavoos-light);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
  }

  :global(.compass .needle) {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, transparent 0%, var(--button-primary-bg) 50%, #ff4500 100%);
    transform-origin: 0 50%;
    transform: translate(0, -50%);
    border-radius: 2px;
    transition: transform 0.8s ease-out;
    z-index: 5;
  }

  :global(.compass .needle::after) {
    content: '';
    position: absolute;
    right: -8px;
    top: -6px;
    width: 0;
    height: 0;
    border-left: 12px solid var(--button-primary-bg);
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
  }

  .qibla-info {
    margin: 1.5rem 0;
  }

  .bearing {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  }

  .degrees-value {
    color: var(--tavoos-light);
  }

  .minutes-value {
    font-size: 1.5rem;
    margin-left: 0.2rem;
  }

  .direction-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-style: italic;
  }

  .mecca-info {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin-top: 2rem;
    padding: 1rem;
    background: var(--bg-overlay);
    border-radius: 0.75rem;
    border: 1px solid var(--border-light);
  }

  .kaaba-icon {
    font-size: 2rem;
  }

  .mecca-text {
    text-align: left;
  }

  .mecca-name {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.95rem;
  }

  .mecca-location {
    color: var(--text-secondary);
    font-size: 0.8rem;
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .compass {
      width: 160px;
      height: 160px;
    }
    
    :global(.compass .needle) {
      width: 65px;
    }
    
    .bearing {
      font-size: 1.5rem;
    }
    
    .section-title {
      font-size: 1.1rem;
    }
    
    .mecca-info {
      flex-direction: column;
      gap: 0.5rem;
      text-align: center;
    }
    
    .mecca-text {
      text-align: center;
    }
  }

  @media (max-width: 480px) {
    .compass {
      width: 140px;
      height: 140px;
    }
    
    :global(.compass .needle) {
      width: 55px;
    }
  }
</style>
