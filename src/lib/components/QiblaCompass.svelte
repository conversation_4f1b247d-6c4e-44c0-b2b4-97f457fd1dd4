<script>
  import { onMount } from 'svelte';

  export let city;

  let compassElement;
  let bearing = 0;
  let degrees = 0;
  let minutes = 0;

  // Calculate Qibla direction
  function calculateQiblaDirection(latitude, longitude) {
    const toRadians = deg => deg * Math.PI / 180;
    const toDegrees = rad => rad * 180 / Math.PI;

    const startLat = toRadians(latitude);
    const startLng = toRadians(longitude);

    // Coordinates of Mecca
    const destLat = toRadians(21.3891);
    const destLng = toRadians(39.8579);

    const y = Math.sin(destLng - startLng) * Math.cos(destLat);
    const x = Math.cos(startLat) * Math.sin(destLat) - Math.sin(startLat) * Math.cos(destLat) * Math.cos(destLng - startLng);
    const brng = toDegrees(Math.atan2(y, x));

    return brng + 180;
  }

  function updateCompass() {
    if (!city || !city.latitude || !city.longitude) return;

    bearing = calculateQiblaDirection(city.latitude, city.longitude);
    degrees = Math.floor(bearing);
    minutes = Math.floor(60 * (bearing - degrees));

    if (compassElement) {
      // Clear the compass
      compassElement.innerHTML = '';

      // Create and position the compass hand
      const hand = document.createElement('div');
      hand.classList.add('hand');
      hand.style.transform = `translate(90px, 90px) translate(0, -2px) rotate(${bearing + 90 - 11.5}deg)`;
      hand.style.zIndex = '20';

      compassElement.appendChild(hand);
    }
  }

  // Update compass when city changes
  $: if (city) {
    updateCompass();
  }

  onMount(() => {
    updateCompass();
  });
</script>

<div class="compass-container">
  <div
    bind:this={compassElement}
    class="compass"
  ></div>

  <div class="bearing">
    <span class="degrees">{degrees}</span>°
    <span class="minutes">{minutes}</span>'
  </div>
</div>

<style>
  .compass-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 1rem 0;
  }

  .compass {
    width: 180px;
    height: 180px;
    background-image: url('./images/compass_rose.png');
    background-position: center;
    background-repeat: no-repeat;
    background-size: 80%;
    background-color: transparent;
    position: relative;
    transition: all 0.3s ease;
    filter: invert(1);
    transform: scale(1.5);
  }

  .compass:hover {
    transform: scale(1.55);
  }

  :global(.compass .hand) {
    width: 70px;
    border: 2px solid black;
    background-color: black;
    position: absolute;
    transform-origin: center left;
    z-index: 10;
  }

  :global(.compass .hand::after) {
    content: '';
    width: 10px;
    border: 1em solid transparent;
    border-left: 1em solid black;
    position: absolute;
    right: -1em;
    top: -1em;
    z-index: 10;
  }

  .bearing {
    text-align: center;
    font-size: 1.1rem;
    font-weight: bold;
    margin-top: 1rem;
  }

  .degrees {
    font-size: 1.2rem;
  }

  .minutes {
    margin-left: 0.1rem;
  }
</style>
