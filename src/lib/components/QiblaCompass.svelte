<script>
  import { qiblaData } from '../stores/appStore.js';
  import { onMount } from 'svelte';

  export let city;

  let compassElement;
  let isAnimating = false;

  // Reactive declarations - automatically recalculate when qiblaData changes
  $: qibla = $qiblaData;
  $: bearing = qibla?.bearing || 0;
  $: degrees = qibla?.degrees || 0;
  $: minutes = qibla?.minutes || 0;
  $: hasValidData = !!(qibla && city);

  // Reactive compass update - this runs whenever bearing changes
  $: if (compassElement && hasValidData) {
    updateCompassHand(bearing);
  }

  function updateCompassHand(newBearing) {
    if (!compassElement || isAnimating) return;

    isAnimating = true;

    // Clear existing hand
    const existingHand = compassElement.querySelector('.hand');
    if (existingHand) {
      existingHand.remove();
    }

    // Create new hand with animation
    const hand = document.createElement('div');
    hand.classList.add('hand');

    // Calculate rotation with smooth transition
    const rotation = newBearing + 90 - 11.5;
    hand.style.transform = `translate(90px, 90px) translate(0, -2px) rotate(${rotation}deg)`;
    hand.style.transition = 'transform 0.5s ease-out';

    compassElement.appendChild(hand);

    // Reset animation flag
    setTimeout(() => {
      isAnimating = false;
    }, 500);
  }

  onMount(() => {
    // Initial compass setup
    if (hasValidData) {
      updateCompassHand(bearing);
    }
  });
</script>

<div class="compass-container" class:active={hasValidData}>
  <div class="compass-wrapper">
    <div
      bind:this={compassElement}
      class="compass"
      class:has-data={hasValidData}
    ></div>

    {#if !hasValidData}
      <div class="compass-placeholder">
        <div class="placeholder-icon">🧭</div>
        <p>Select location to see Qibla</p>
      </div>
    {/if}
  </div>

  {#if hasValidData}
    <div class="bearing" class:highlight={hasValidData}>
      <div class="bearing-display">
        <span class="degrees">{degrees}</span>
        <span class="degree-symbol">°</span>
        <span class="minutes">{minutes}</span>
        <span class="minute-symbol">'</span>
      </div>
      <div class="bearing-label">to Mecca</div>
    </div>
  {/if}
</div>

<style>
  .compass-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 1.5rem 0;
    transition: all 0.3s ease;
  }

  .compass-container.active {
    transform: scale(1.02);
  }

  .compass-wrapper {
    position: relative;
    width: 180px;
    height: 180px;
  }

  .compass {
    width: 100%;
    height: 100%;
    background-image: url('./images/compass_rose.png');
    background-position: center;
    background-repeat: no-repeat;
    background-size: 80%;
    background-color: transparent;
    position: relative;
    transition: all 0.4s ease;
    filter: invert(1);
    transform: scale(1.4);
    opacity: 0.3;
  }

  .compass.has-data {
    opacity: 1;
    transform: scale(1.5);
  }

  .compass:hover.has-data {
    transform: scale(1.6);
  }

  .compass-placeholder {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: var(--text-secondary);
  }

  .placeholder-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    opacity: 0.5;
  }

  .compass-placeholder p {
    font-size: 0.8rem;
    margin: 0;
    max-width: 120px;
  }

  :global(.compass .hand) {
    width: 70px;
    height: 3px;
    background: linear-gradient(90deg, #000 0%, #333 100%);
    border: 1px solid #000;
    position: absolute;
    transform-origin: center left;
    z-index: 10;
    border-radius: 2px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.3);
  }

  :global(.compass .hand::after) {
    content: '';
    width: 0;
    height: 0;
    border: 8px solid transparent;
    border-left: 12px solid #000;
    position: absolute;
    right: -12px;
    top: -7px;
    z-index: 10;
  }

  .bearing {
    text-align: center;
    margin-top: 1.5rem;
    transition: all 0.3s ease;
    opacity: 0.8;
  }

  .bearing.highlight {
    opacity: 1;
    transform: translateY(-2px);
  }

  .bearing-display {
    font-size: 1.3rem;
    font-weight: bold;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
  }

  .degrees {
    font-size: 1.5rem;
    color: var(--tavoos-light);
  }

  .degree-symbol {
    font-size: 1.2rem;
    margin-right: 0.25rem;
  }

  .minutes {
    font-size: 1.1rem;
  }

  .minute-symbol {
    font-size: 1rem;
  }

  .bearing-label {
    font-size: 0.8rem;
    color: var(--text-secondary);
    font-style: italic;
  }

  /* Pulse animation for active state */
  @keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.02); }
  }

  .compass-container.active .compass.has-data {
    animation: pulse 3s ease-in-out infinite;
  }
</style>
