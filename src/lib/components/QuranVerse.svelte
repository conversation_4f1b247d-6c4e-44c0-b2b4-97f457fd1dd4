<script>
  export let verse;
  export let onRefresh = () => {};

  let audioElement;
  let isPlaying = false;
  let currentTime = 0;
  let duration = 0;
  let isLoading = false;

  function toggleAudio() {
    if (!audioElement) return;

    if (isPlaying) {
      audioElement.pause();
    } else {
      audioElement.play();
    }
  }

  function handleAudioPlay() {
    isPlaying = true;
  }

  function handleAudioPause() {
    isPlaying = false;
  }

  function handleAudioTimeUpdate() {
    if (audioElement) {
      currentTime = audioElement.currentTime;
    }
  }

  function handleAudioLoadedMetadata() {
    if (audioElement) {
      duration = audioElement.duration;
    }
    isLoading = false;
  }

  function handleAudioLoadStart() {
    isLoading = true;
  }

  function handleAudioError() {
    isLoading = false;
    console.error('Audio failed to load');
  }

  function formatTime(seconds) {
    if (!seconds || isNaN(seconds)) return '0:00';

    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  }

  function handleProgressClick(event) {
    if (!audioElement || !duration) return;

    const rect = event.currentTarget.getBoundingClientRect();
    const clickX = event.clientX - rect.left;
    const percentage = clickX / rect.width;
    const newTime = percentage * duration;

    audioElement.currentTime = newTime;
  }

  function handleProgressKeydown(event) {
    if (!audioElement || !duration) return;

    let newTime = currentTime;
    const step = duration * 0.05; // 5% steps

    switch (event.key) {
      case 'ArrowLeft':
        newTime = Math.max(0, currentTime - step);
        break;
      case 'ArrowRight':
        newTime = Math.min(duration, currentTime + step);
        break;
      case 'Home':
        newTime = 0;
        break;
      case 'End':
        newTime = duration;
        break;
      default:
        return; // Don't prevent default for other keys
    }

    event.preventDefault();
    audioElement.currentTime = newTime;
  }

  function refreshVerse() {
    onRefresh();
  }

  function openVerseLink() {
    if (verse?.webUrl) {
      window.open(verse.webUrl, '_blank');
    }
  }

  $: progressPercentage = duration > 0 ? (currentTime / duration) * 100 : 0;
</script>

<div class="quran-verse card">
  <div class="verse-header">
    <h3 class="section-title">📖 Quran Verse</h3>
    <button class="refresh-btn" on:click={refreshVerse} title="Get new verse">
      🔄
    </button>
  </div>

  {#if verse}
    <div class="verse-content">
      <div class="arabic-text">
        {verse.arabic}
      </div>

      <div class="verse-reference">
        {#if verse.chapterName}
          {verse.chapterName} {verse.chapterIndex}:{verse.verseIndex}
        {:else}
          {verse.chapterIndex}:{verse.verseIndex}
        {/if}
      </div>

      <div class="audio-controls">
        <audio
          bind:this={audioElement}
          src={verse.audioUrl}
          preload="metadata"
          on:play={handleAudioPlay}
          on:pause={handleAudioPause}
          on:timeupdate={handleAudioTimeUpdate}
          on:loadedmetadata={handleAudioLoadedMetadata}
          on:loadstart={handleAudioLoadStart}
          on:error={handleAudioError}
        ></audio>

        <button
          class="play-btn"
          on:click={toggleAudio}
          disabled={isLoading}
          title={isPlaying ? 'Pause' : 'Play'}
        >
          {#if isLoading}
            ⏳
          {:else if isPlaying}
            ⏸️
          {:else}
            ▶️
          {/if}
        </button>

        <div
          class="progress-container"
          role="slider"
          tabindex="0"
          aria-label="Audio progress"
          aria-valuemin="0"
          aria-valuemax="100"
          aria-valuenow={Math.round(progressPercentage)}
          on:click={handleProgressClick}
          on:keydown={handleProgressKeydown}
        >
          <div class="progress-bar">
            <div
              class="progress-fill"
              style="width: {progressPercentage}%"
            ></div>
          </div>
        </div>

        <div class="time-display">
          <span class="current-time">{formatTime(currentTime)}</span>
          <span class="duration">{formatTime(duration)}</span>
        </div>
      </div>

      <div class="verse-actions">
        <button class="btn-secondary" on:click={openVerseLink}>
          Read More
        </button>
      </div>
    </div>
  {:else}
    <div class="loading-verse">
      <div class="spinner"></div>
      <p>Loading verse...</p>
    </div>
  {/if}
</div>

<style>
  .quran-verse {
    animation: fadeIn 0.8s ease-out;
  }

  .verse-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
  }

  .section-title {
    font-size: 1.2rem;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0;
  }

  .refresh-btn {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
  }

  .refresh-btn:hover {
    background: var(--bg-overlay);
    transform: rotate(180deg);
  }

  .verse-content {
    text-align: center;
  }

  .arabic-text {
    font-family: 'me_quran', Arial, sans-serif;
    font-size: 1.8rem;
    line-height: 1.8;
    color: var(--text-primary);
    direction: rtl;
    text-align: center;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: var(--bg-overlay);
    border-radius: 0.75rem;
    border: 1px solid var(--border-light);
  }

  .verse-reference {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 600;
    margin-bottom: 1.5rem;
  }

  .audio-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: var(--bg-overlay);
    border-radius: 0.75rem;
    border: 1px solid var(--border-light);
  }

  .play-btn {
    background: var(--button-primary-bg);
    color: var(--button-primary-text);
    border: 1px solid var(--button-secondary-border);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    flex-shrink: 0;
  }

  .play-btn:hover {
    background: white;
    transform: scale(1.1);
  }

  .play-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }

  .progress-container {
    flex: 1;
    cursor: pointer;
  }

  .progress-bar {
    width: 100%;
    height: 6px;
    background: var(--border-medium);
    border-radius: 3px;
    overflow: hidden;
  }

  .progress-fill {
    height: 100%;
    background: var(--tavoos-light);
    transition: width 0.1s ease;
  }

  .time-display {
    display: flex;
    gap: 0.5rem;
    font-size: 0.8rem;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    color: var(--text-secondary);
    flex-shrink: 0;
  }

  .current-time {
    color: var(--text-primary);
  }

  .verse-actions {
    display: flex;
    justify-content: center;
  }

  .loading-verse {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    padding: 2rem;
  }

  .spinner {
    width: 30px;
    height: 30px;
    border: 3px solid var(--bg-overlay);
    border-top: 3px solid var(--tavoos-light);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .arabic-text {
      font-size: 1.5rem;
      line-height: 1.6;
    }

    .audio-controls {
      gap: 0.75rem;
      padding: 0.75rem;
    }

    .play-btn {
      width: 35px;
      height: 35px;
      font-size: 0.9rem;
    }

    .section-title {
      font-size: 1.1rem;
    }
  }

  @media (max-width: 480px) {
    .arabic-text {
      font-size: 1.3rem;
      padding: 0.75rem;
    }

    .audio-controls {
      flex-direction: column;
      gap: 0.75rem;
    }

    .progress-container {
      width: 100%;
    }

    .time-display {
      justify-content: center;
    }
  }
</style>
