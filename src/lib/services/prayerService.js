// Prayer Service for Tavoos Svelte Extension
class PrayerService {
  constructor() {
    this.baseUrl = 'https://behnegar.app/api/v1/tavoos/pray-times-v2';
    this.cache = new Map();
  }

  async getTodaysPrayerTimes(latitude, longitude) {
    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
    const cacheKey = `${latitude}_${longitude}_${today}`;
    
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    try {
      const response = await fetch(
        `${this.baseUrl}/coordinates/${latitude}/${longitude}/schedule?period=today`
      );
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.success && data.data && data.data.length > 0) {
        const todayData = data.data[0];
        
        // Map to user-friendly prayer names
        const prayerTimes = {
          dawn: { name: 'Dawn', time: todayData.fajr, key: 'fajr' },
          noon: { name: 'Noon', time: todayData.dhur || todayData.dhohr, key: 'dhur' },
          afternoon: { name: 'Afternoon', time: todayData.asr || todayData.aser, key: 'asr' },
          dusk: { name: 'Dusk', time: todayData.maghrib || todayData.maghreb, key: 'maghrib' },
          dinner: { name: 'Dinner', time: todayData.isha, key: 'isha' }
        };

        // Add current prayer and next prayer info
        const enrichedData = this.enrichPrayerTimes(prayerTimes);
        
        this.cache.set(cacheKey, enrichedData);
        return enrichedData;
      } else {
        throw new Error('Invalid prayer times data format');
      }
    } catch (error) {
      console.error('Error fetching prayer times:', error);
      
      // Return fallback prayer times (approximate for Belgium)
      return this.getFallbackPrayerTimes();
    }
  }

  enrichPrayerTimes(prayerTimes) {
    const now = new Date();
    const currentTime = now.getHours() * 60 + now.getMinutes(); // minutes since midnight
    
    // Convert prayer times to minutes since midnight
    const prayers = Object.entries(prayerTimes).map(([key, prayer]) => {
      const timeMinutes = this.timeStringToMinutes(prayer.time);
      return {
        ...prayer,
        key,
        timeMinutes,
        isPassed: timeMinutes < currentTime
      };
    }).sort((a, b) => a.timeMinutes - b.timeMinutes);

    // Find current and next prayer
    let currentPrayer = null;
    let nextPrayer = null;
    
    for (let i = 0; i < prayers.length; i++) {
      if (!prayers[i].isPassed) {
        nextPrayer = prayers[i];
        currentPrayer = i > 0 ? prayers[i - 1] : prayers[prayers.length - 1];
        break;
      }
    }
    
    // If all prayers have passed, next prayer is tomorrow's first prayer
    if (!nextPrayer) {
      nextPrayer = prayers[0];
      currentPrayer = prayers[prayers.length - 1];
      nextPrayer.isTomorrow = true;
    }

    return {
      prayers: prayerTimes,
      currentPrayer,
      nextPrayer,
      timeUntilNext: this.calculateTimeUntilNext(nextPrayer, currentTime)
    };
  }

  timeStringToMinutes(timeString) {
    if (!timeString) return 0;
    const [hours, minutes] = timeString.split(':').map(Number);
    return hours * 60 + minutes;
  }

  calculateTimeUntilNext(nextPrayer, currentTimeMinutes) {
    if (!nextPrayer) return null;
    
    let targetTime = nextPrayer.timeMinutes;
    
    // If it's tomorrow's prayer, add 24 hours
    if (nextPrayer.isTomorrow) {
      targetTime += 24 * 60;
    }
    
    const diffMinutes = targetTime - currentTimeMinutes;
    
    if (diffMinutes <= 0) return null;
    
    const hours = Math.floor(diffMinutes / 60);
    const minutes = diffMinutes % 60;
    
    return { hours, minutes, totalMinutes: diffMinutes };
  }

  getFallbackPrayerTimes() {
    // Approximate prayer times for Belgium (will vary by season)
    const now = new Date();
    const month = now.getMonth(); // 0-11
    
    // Rough seasonal adjustments
    let fajr = '05:30';
    let dhur = '12:30';
    let asr = '15:30';
    let maghrib = '18:00';
    let isha = '19:30';
    
    // Summer adjustments (May-August)
    if (month >= 4 && month <= 7) {
      fajr = '04:30';
      maghrib = '20:30';
      isha = '22:00';
    }
    // Winter adjustments (November-February)
    else if (month >= 10 || month <= 1) {
      fajr = '06:30';
      maghrib = '16:30';
      isha = '18:00';
    }

    const prayerTimes = {
      dawn: { name: 'Dawn', time: fajr, key: 'fajr' },
      noon: { name: 'Noon', time: dhur, key: 'dhur' },
      afternoon: { name: 'Afternoon', time: asr, key: 'asr' },
      dusk: { name: 'Dusk', time: maghrib, key: 'maghrib' },
      dinner: { name: 'Dinner', time: isha, key: 'isha' }
    };

    return this.enrichPrayerTimes(prayerTimes);
  }

  // Format time for display
  formatTime(timeString) {
    if (!timeString) return '';
    
    try {
      const [hours, minutes] = timeString.split(':');
      const hour24 = parseInt(hours);
      const hour12 = hour24 === 0 ? 12 : hour24 > 12 ? hour24 - 12 : hour24;
      const ampm = hour24 >= 12 ? 'PM' : 'AM';
      
      return `${hour12}:${minutes} ${ampm}`;
    } catch (error) {
      return timeString;
    }
  }

  // Clear cache (useful for testing)
  clearCache() {
    this.cache.clear();
  }
}

// Export singleton instance
export const prayerService = new PrayerService();
