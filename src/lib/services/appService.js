import { get } from 'svelte/store';
import { actions, location, availableData } from '../stores/appStore.js';
import { locationService } from './locationService.js';

// Main app initialization - this is the entry point
export async function initializeApp() {
  try {
    actions.setLoading(true);
    actions.clearError();

    // Load continents first
    const continents = await locationService.getContinents();
    actions.setAvailableData('continents', continents);

    // Try to restore saved location
    const savedLocation = await locationService.getStoredLocation();
    
    if (savedLocation.continent && savedLocation.country && savedLocation.city) {
      // Restore complete saved location
      await restoreLocation(savedLocation);
    } else {
      // Initialize with default location
      await initializeDefaultLocation(continents);
    }

  } catch (err) {
    console.error('App initialization failed:', err);
    actions.setError('Failed to initialize app. Please try again.');
  } finally {
    actions.setLoading(false);
  }
}

// Restore a saved location - reactive restoration
async function restoreLocation(savedLocation) {
  try {
    // Set the location in store
    actions.updateLocation(savedLocation);

    // Load countries for the continent
    const countries = await locationService.getCountries(savedLocation.continent.id);
    actions.setAvailableData('countries', countries);

    // Load cities for the country
    const cities = await locationService.getCities(savedLocation.country.id);
    actions.setAvailableData('cities', cities);

  } catch (err) {
    console.error('Failed to restore location:', err);
    // Fall back to default location
    const continents = get(availableData).continents;
    await initializeDefaultLocation(continents);
  }
}

// Initialize with default location (EU -> BE -> Gent)
async function initializeDefaultLocation(continents) {
  try {
    // Find Europe continent
    let continent = continents.find(c => c.code === 'EU');
    if (!continent) continent = continents[0];

    if (!continent) throw new Error('No continents available');

    // Load countries for Europe
    const countries = await locationService.getCountries(continent.id);
    actions.setAvailableData('countries', countries);

    // Find Belgium
    let country = countries.find(c => c.code === 'BE');
    if (!country) country = countries[0];

    if (!country) throw new Error('No countries available');

    // Load cities for Belgium
    const cities = await locationService.getCities(country.id);
    actions.setAvailableData('cities', cities);

    // Find Gent
    let city = cities.find(c => c.name === 'Gent');
    if (!city) city = cities[0];

    if (!city) throw new Error('No cities available');

    // Set the default location
    const defaultLocation = { continent, country, city };
    actions.updateLocation(defaultLocation);

    // Save it
    await locationService.storeLocation(continent, country, city);

  } catch (err) {
    console.error('Failed to initialize default location:', err);
    actions.setError('Failed to load default location');
  }
}

// Handle continent selection - reactive selection
export async function selectContinent(continent) {
  try {
    actions.selectContinent(continent);
    actions.setAvailableData('countries', []);
    actions.setAvailableData('cities', []);

    if (!continent) return;

    // Load countries for selected continent
    const countries = await locationService.getCountries(continent.id);
    actions.setAvailableData('countries', countries);

  } catch (err) {
    console.error('Failed to load countries:', err);
    actions.setError('Failed to load countries');
  }
}

// Handle country selection - reactive selection
export async function selectCountry(country) {
  try {
    actions.selectCountry(country);
    actions.setAvailableData('cities', []);

    if (!country) return;

    // Load cities for selected country
    const cities = await locationService.getCities(country.id);
    actions.setAvailableData('cities', cities);

  } catch (err) {
    console.error('Failed to load cities:', err);
    actions.setError('Failed to load cities');
  }
}

// Handle city selection - reactive selection with persistence
export async function selectCity(city) {
  try {
    actions.selectCity(city);

    if (!city) return;

    // Save the complete location
    const currentLocation = get(location);
    if (currentLocation.continent && currentLocation.country) {
      await locationService.storeLocation(
        currentLocation.continent,
        currentLocation.country,
        city
      );
    }

  } catch (err) {
    console.error('Failed to save city selection:', err);
    actions.setError('Failed to save location');
  }
}
