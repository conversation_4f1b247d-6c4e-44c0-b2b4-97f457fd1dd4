// Quran Service for Tavoos Svelte Extension
class QuranService {
  constructor() {
    this.baseUrl = 'https://behnegar.app/api/v1/tavoos/quran/verses';
    this.audioBaseUrl = 'https://file-keeper.com/media/audio/recitation/mohammad-seddigh-menshawi/morattal';
    this.cache = new Map();
    this.totalVerses = 6236; // Total verses in Quran
  }

  async getRandomVerse() {
    // Generate random verse ID (1-6236)
    const verseId = Math.floor(Math.random() * this.totalVerses) + 1;
    return await this.getVerse(verseId);
  }

  async getVerse(verseId) {
    if (this.cache.has(verseId)) {
      return this.cache.get(verseId);
    }

    try {
      const response = await fetch(`${this.baseUrl}/${verseId}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.success && data.data) {
        const verse = {
          id: verseId,
          arabic: data.data.text || data.data.arabic,
          chapterIndex: data.data.chapter_index || data.data.chapterIndex,
          verseIndex: data.data.verse_index || data.data.verseIndex,
          chapterName: data.data.chapter_name || data.data.chapterName,
          audioUrl: `${this.audioBaseUrl}/${verseId}.mp3`,
          webUrl: `https://tavoos.eu/quran/verse/${data.data.chapter_index || data.data.chapterIndex}/${data.data.verse_index || data.data.verseIndex}`
        };

        this.cache.set(verseId, verse);
        return verse;
      } else {
        throw new Error('Invalid verse data format');
      }
    } catch (error) {
      console.error('Error fetching verse:', error);
      
      // Return fallback verse (Al-Fatiha 1:1)
      return this.getFallbackVerse();
    }
  }

  getFallbackVerse() {
    return {
      id: 1,
      arabic: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
      chapterIndex: 1,
      verseIndex: 1,
      chapterName: 'Al-Fatiha',
      audioUrl: `${this.audioBaseUrl}/1.mp3`,
      webUrl: 'https://tavoos.eu/quran/verse/1/1'
    };
  }

  // Get verse by chapter and verse number
  async getVerseByReference(chapterIndex, verseIndex) {
    // This would require a mapping from chapter/verse to verse ID
    // For now, return a random verse
    return await this.getRandomVerse();
  }

  // Format verse reference
  formatReference(chapterIndex, verseIndex, chapterName) {
    if (chapterName) {
      return `${chapterName} ${chapterIndex}:${verseIndex}`;
    }
    return `${chapterIndex}:${verseIndex}`;
  }

  // Check if audio is available
  async checkAudioAvailability(audioUrl) {
    try {
      const response = await fetch(audioUrl, { method: 'HEAD' });
      return response.ok;
    } catch (error) {
      return false;
    }
  }

  // Get daily verse (same verse for the whole day)
  async getDailyVerse() {
    const today = new Date();
    const dayOfYear = Math.floor((today - new Date(today.getFullYear(), 0, 0)) / 86400000);
    
    // Use day of year to get consistent daily verse
    const verseId = (dayOfYear % this.totalVerses) + 1;
    
    return await this.getVerse(verseId);
  }

  // Clear cache (useful for testing)
  clearCache() {
    this.cache.clear();
  }
}

// Export singleton instance
export const quranService = new QuranService();
