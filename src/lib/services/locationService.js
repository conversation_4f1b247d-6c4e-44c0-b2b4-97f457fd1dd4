// Location Service for Tavoos Svelte Extension
class LocationService {
  constructor() {
    this.baseUrl = 'https://behnegar.app/api/v1/tavoos/pray-times-v2';
    this.cache = {
      continents: null,
      countries: new Map(),
      cities: new Map()
    };
  }

  async initialize() {
    // Pre-load continents for faster UX
    try {
      await this.getContinents();
    } catch (error) {
      console.warn('Failed to pre-load continents:', error);
    }
  }

  async getContinents() {
    if (this.cache.continents) {
      return this.cache.continents;
    }

    try {
      const response = await fetch(`${this.baseUrl}/continents`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.success && Array.isArray(data.data)) {
        this.cache.continents = data.data;
        return data.data;
      } else {
        throw new Error('Invalid continents data format');
      }
    } catch (error) {
      console.error('Error fetching continents:', error);
      // Return fallback data
      return [
        { id: 1, name: 'Europe', code: 'EU' },
        { id: 2, name: 'Asia', code: 'AS' },
        { id: 3, name: 'Africa', code: 'AF' },
        { id: 4, name: 'North America', code: 'NA' },
        { id: 5, name: 'South America', code: 'SA' },
        { id: 6, name: 'Oceania', code: 'OC' }
      ];
    }
  }

  async getCountries(continentId) {
    const cacheKey = `continent_${continentId}`;
    
    if (this.cache.countries.has(cacheKey)) {
      return this.cache.countries.get(cacheKey);
    }

    try {
      const response = await fetch(`${this.baseUrl}/continents/${continentId}/countries`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.success && Array.isArray(data.data)) {
        this.cache.countries.set(cacheKey, data.data);
        return data.data;
      } else {
        throw new Error('Invalid countries data format');
      }
    } catch (error) {
      console.error('Error fetching countries:', error);
      // Return fallback for Europe
      if (continentId == 1) {
        return [
          { id: 1, name: 'Belgium', code: 'BE' },
          { id: 2, name: 'Netherlands', code: 'NL' },
          { id: 3, name: 'Germany', code: 'DE' },
          { id: 4, name: 'France', code: 'FR' }
        ];
      }
      return [];
    }
  }

  async getCities(countryId) {
    const cacheKey = `country_${countryId}`;
    
    if (this.cache.cities.has(cacheKey)) {
      return this.cache.cities.get(cacheKey);
    }

    try {
      const response = await fetch(`${this.baseUrl}/countries/${countryId}/cities`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.success && Array.isArray(data.data)) {
        this.cache.cities.set(cacheKey, data.data);
        return data.data;
      } else {
        throw new Error('Invalid cities data format');
      }
    } catch (error) {
      console.error('Error fetching cities:', error);
      // Return fallback for Belgium
      if (countryId == 1) {
        return [
          { id: 1, name: 'Gent', latitude: 51.0543, longitude: 3.7174 },
          { id: 2, name: 'Brussels', latitude: 50.8503, longitude: 4.3517 },
          { id: 3, name: 'Antwerp', latitude: 51.2194, longitude: 4.4025 }
        ];
      }
      return [];
    }
  }

  async getStoredLocation() {
    try {
      const result = await chrome.storage.sync.get([
        'tavoos_selected_continent',
        'tavoos_selected_country', 
        'tavoos_selected_city'
      ]);

      return {
        continent: result.tavoos_selected_continent || null,
        country: result.tavoos_selected_country || null,
        city: result.tavoos_selected_city || null
      };
    } catch (error) {
      console.error('Error getting stored location:', error);
      return { continent: null, country: null, city: null };
    }
  }

  async storeLocation(continent, country, city) {
    try {
      await chrome.storage.sync.set({
        tavoos_selected_continent: continent,
        tavoos_selected_country: country,
        tavoos_selected_city: city
      });
      
      console.log('Location stored successfully:', {
        continent: continent?.name,
        country: country?.name,
        city: city?.name
      });
    } catch (error) {
      console.error('Error storing location:', error);
      throw error;
    }
  }

  // Calculate Qibla direction
  calculateQiblaDirection(latitude, longitude) {
    const toRadians = deg => deg * Math.PI / 180;
    const toDegrees = rad => rad * 180 / Math.PI;

    const startLat = toRadians(latitude);
    const startLng = toRadians(longitude);

    // Coordinates of Mecca (Kaaba)
    const destLat = toRadians(21.3891);
    const destLng = toRadians(39.8579);

    const y = Math.sin(destLng - startLng) * Math.cos(destLat);
    const x = Math.cos(startLat) * Math.sin(destLat) - 
              Math.sin(startLat) * Math.cos(destLat) * Math.cos(destLng - startLng);
    
    const bearing = toDegrees(Math.atan2(y, x));
    
    // Normalize to 0-360 degrees
    return (bearing + 360) % 360;
  }

  // Clear cache (useful for testing)
  clearCache() {
    this.cache = {
      continents: null,
      countries: new Map(),
      cities: new Map()
    };
  }
}

// Export singleton instance
export const locationService = new LocationService();
