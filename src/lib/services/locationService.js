// Pure service - no store dependencies for better separation of concerns

class LocationService {
  constructor() {
    this.baseUrl = 'https://behnegar.app/api/v1/tavoos/pray-times-v2';
    this.storageKeys = {
      continents: 'tavoos_continents',
      selectedContinent: 'tavoos_selected_continent',
      selectedCountry: 'tavoos_selected_country',
      selectedCity: 'tavoos_selected_city'
    };

    // Default fallback values
    this.defaults = {
      continent: { code: 'EU', name: 'Europe' },
      country: {
        europe: { code: 'BE', name: 'Belgium' },
        asia: { code: 'IR', name: 'Iran' }
      },
      city: { name: 'Gent' }
    };
  }

  // Fetch continents from API or cache
  async getContinents() {
    try {
      // Try to get from cache first
      const cached = await this.getFromStorage(this.storageKeys.continents);
      if (cached && cached.length > 0) {
        return cached;
      }

      // Fetch from API
      const response = await fetch(`${this.baseUrl}/continents`);
      if (!response.ok) {
        throw new Error(`Failed to fetch continents: ${response.status}`);
      }

      const continents = await response.json();

      // Cache the result (continents are small, should be fine)
      try {
        await this.saveToStorage(this.storageKeys.continents, continents);
      } catch (storageError) {
        console.warn('Could not cache continents:', storageError);
      }

      return continents;
    } catch (error) {
      console.error('Error fetching continents:', error);
      return [];
    }
  }

  // Fetch countries for a continent from API (no caching due to size limits)
  async getCountries(continentId) {
    try {
      const response = await fetch(`${this.baseUrl}/continents/${continentId}/countries`);
      if (!response.ok) {
        throw new Error(`Failed to fetch countries: ${response.status}`);
      }

      const countries = await response.json();
      return countries;
    } catch (error) {
      console.error('Error fetching countries:', error);
      return [];
    }
  }

  // Fetch cities for a country from API (no caching due to size limits)
  async getCities(countryId) {
    try {
      const response = await fetch(`${this.baseUrl}/countries/${countryId}/cities`);
      if (!response.ok) {
        throw new Error(`Failed to fetch cities: ${response.status}`);
      }

      const cities = await response.json();
      return cities;
    } catch (error) {
      console.error('Error fetching cities:', error);
      return [];
    }
  }

  // Get stored location selection
  async getStoredLocation() {
    try {
      let continent = null;
      let country = null;
      let city = null;

      try {
        continent = await this.getFromStorage(this.storageKeys.selectedContinent);
      } catch (error) {
        console.warn('Could not retrieve continent from storage:', error);
      }

      try {
        country = await this.getFromStorage(this.storageKeys.selectedCountry);
      } catch (error) {
        console.warn('Could not retrieve country from storage:', error);
      }

      try {
        city = await this.getFromStorage(this.storageKeys.selectedCity);
      } catch (error) {
        console.warn('Could not retrieve city from storage:', error);
      }

      return { continent, country, city };
    } catch (error) {
      console.error('Error getting stored location:', error);
      return { continent: null, country: null, city: null };
    }
  }

  // Store location selection
  async storeLocation(continent, country, city) {
    try {
      // Store each item separately with error handling
      try {
        await this.saveToStorage(this.storageKeys.selectedContinent, continent);
      } catch (error) {
        console.warn('Could not save continent:', error);
      }

      try {
        await this.saveToStorage(this.storageKeys.selectedCountry, country);
      } catch (error) {
        console.warn('Could not save country:', error);
      }

      try {
        await this.saveToStorage(this.storageKeys.selectedCity, city);
      } catch (error) {
        console.warn('Could not save city:', error);
      }

      // Store updated - no direct store manipulation in service
    } catch (error) {
      console.error('Error storing location:', error);
    }
  }

  // Initialize with default location (EU -> BE -> Gent)
  async initializeDefaultLocation() {
    try {
      // Get continents
      const continents = await this.getContinents();
      if (continents.length === 0) {
        throw new Error('No continents available');
      }

      // Find Europe continent
      let selectedContinent = continents.find(c => c.code === this.defaults.continent.code);
      if (!selectedContinent) {
        selectedContinent = continents[0]; // Fallback to first continent
      }

      // Get countries for the selected continent
      const countries = await this.getCountries(selectedContinent.id);
      if (countries.length === 0) {
        throw new Error('No countries available');
      }

      // Find default country based on continent
      let selectedCountry;
      if (selectedContinent.code === 'EU') {
        selectedCountry = countries.find(c => c.code === this.defaults.country.europe.code);
      } else if (selectedContinent.code === 'AS') {
        selectedCountry = countries.find(c => c.code === this.defaults.country.asia.code);
      }

      if (!selectedCountry) {
        selectedCountry = countries[0]; // Fallback to first country
      }

      // Get cities for the selected country
      const cities = await this.getCities(selectedCountry.id);
      if (cities.length === 0) {
        throw new Error('No cities available');
      }

      // Find Gent or fallback to first city
      let selectedCity = cities.find(c => c.name === this.defaults.city.name);
      if (!selectedCity) {
        selectedCity = cities[0]; // Fallback to first city
      }

      // Store the default selection
      await this.storeLocation(selectedContinent, selectedCountry, selectedCity);

      return { continent: selectedContinent, country: selectedCountry, city: selectedCity };
    } catch (error) {
      console.error('Error initializing default location:', error);
      return null;
    }
  }

  // Initialize location data
  async initializeLocationData() {
    try {
      // Try to load stored location first
      const storedLocation = await this.getStoredLocation();

      if (storedLocation.continent && storedLocation.country && storedLocation.city) {
        // Use stored location
        locationStore.set(storedLocation);
        return storedLocation;
      } else {
        // Initialize with default location
        const defaultLocation = await this.initializeDefaultLocation();
        return defaultLocation;
      }
    } catch (error) {
      console.error('Error initializing location data:', error);
      return null;
    }
  }

  // Helper methods for Chrome storage
  async getFromStorage(key) {
    return new Promise((resolve, reject) => {
      chrome.storage.sync.get([key], (result) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve(result[key] || null);
        }
      });
    });
  }

  async saveToStorage(key, value) {
    return new Promise((resolve, reject) => {
      chrome.storage.sync.set({ [key]: value }, () => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve();
        }
      });
    });
  }
}

// Export the service instance
export const locationService = new LocationService();
