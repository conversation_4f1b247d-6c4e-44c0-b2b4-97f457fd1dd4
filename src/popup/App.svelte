<script>
  import { onMount } from 'svelte';
  import LocationSelector from '../lib/components/LocationSelector.svelte';
  import QiblaCompass from '../lib/components/QiblaCompass.svelte';
  import TavoosLogo from '../lib/components/TavoosLogo.svelte';
  import LoadingSpinner from '../lib/components/LoadingSpinner.svelte';
  import ErrorMessage from '../lib/components/ErrorMessage.svelte';
  import { location, isLoading, error } from '../lib/stores/appStore.js';
  import { initializeApp } from '../lib/services/appService.js';

  // Local reactive variables
  let currentLocation = { continent: null, country: null, city: null };
  let loading = true;
  let errorMessage = null;

  // Subscribe to stores
  location.subscribe(value => {
    currentLocation = value;
  });

  isLoading.subscribe(value => {
    loading = value;
  });

  error.subscribe(value => {
    errorMessage = value;
  });

  // Reactive statements
  $: hasLocation = currentLocation.city && currentLocation.country && currentLocation.continent;
  $: locationText = hasLocation
    ? `${currentLocation.city.name}, ${currentLocation.country.name}`
    : 'No location selected';

  onMount(async () => {
    try {
      await initializeApp();
    } catch (err) {
      console.error('Failed to initialize app:', err);
    }
  });
</script>

<main class="app">
  <TavoosLogo />

  {#if loading}
    <LoadingSpinner message="Loading location data..." />
  {:else if errorMessage}
    <ErrorMessage error={errorMessage} />
  {:else}
    <div class="content">
      <LocationSelector />

      {#if hasLocation}
        <QiblaCompass city={currentLocation.city} />
        <div class="location-display" class:highlight={hasLocation}>
          {locationText}
        </div>
      {:else}
        <div class="no-location">
          Please select your location to see Qibla direction
        </div>
      {/if}
    </div>
  {/if}
</main>

<style>
  .app {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    min-height: 100%;
    padding: 0.5rem;
  }

  .content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    width: 100%;
  }

  .location-display {
    text-align: center;
    font-weight: bold;
    font-size: 0.9rem;
    color: var(--text-secondary);
    transition: all 0.3s ease;
    padding: 0.5rem;
    border-radius: 0.5rem;
  }

  .location-display.highlight {
    color: var(--text-primary);
    background: var(--bg-overlay);
  }

  .no-location {
    text-align: center;
    color: var(--text-secondary);
    font-style: italic;
    padding: 2rem 1rem;
  }
</style>
