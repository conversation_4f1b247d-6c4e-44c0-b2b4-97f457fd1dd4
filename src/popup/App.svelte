<script>
  import { onMount } from 'svelte';
  import LocationSelector from '../lib/components/LocationSelector.svelte';
  import QiblaCompass from '../lib/components/QiblaCompass.svelte';
  import TavoosLogo from '../lib/components/TavoosLogo.svelte';
  import LoadingSpinner from '../lib/components/LoadingSpinner.svelte';
  import ErrorMessage from '../lib/components/ErrorMessage.svelte';
  import { locationService } from '../lib/services/locationService.js';

  // Simple reactive state - no stores needed
  let loading = true;
  let errorMessage = null;
  let currentLocation = { continent: null, country: null, city: null };
  let availableData = { continents: [], countries: [], cities: [] };

  // Reactive statements
  $: hasLocation = currentLocation.city && currentLocation.country && currentLocation.continent;
  $: locationText = hasLocation
    ? `${currentLocation.city.name}, ${currentLocation.country.name}`
    : 'No location selected';

  async function initializeApp() {
    try {
      loading = true;
      errorMessage = null;

      // Load continents
      const continents = await locationService.getContinents();
      availableData = { ...availableData, continents };

      // Try to restore saved location
      const savedLocation = await locationService.getStoredLocation();

      if (savedLocation.continent && savedLocation.country && savedLocation.city) {
        // Restore complete saved location
        currentLocation = savedLocation;

        // Load countries and cities for the saved location
        const countries = await locationService.getCountries(savedLocation.continent.id);
        const cities = await locationService.getCities(savedLocation.country.id);
        availableData = { continents, countries, cities };
      } else {
        // Initialize with default location
        await initializeDefaultLocation(continents);
      }
    } catch (err) {
      console.error('App initialization failed:', err);
      errorMessage = 'Failed to initialize app. Please try again.';
    } finally {
      loading = false;
    }
  }

  async function initializeDefaultLocation(continents) {
    try {
      // Find Europe continent
      let continent = continents.find(c => c.code === 'EU');
      if (!continent) continent = continents[0];

      if (!continent) throw new Error('No continents available');

      // Load countries for Europe
      const countries = await locationService.getCountries(continent.id);
      availableData = { ...availableData, countries };

      // Find Belgium
      let country = countries.find(c => c.code === 'BE');
      if (!country) country = countries[0];

      if (!country) throw new Error('No countries available');

      // Load cities for Belgium
      const cities = await locationService.getCities(country.id);
      availableData = { ...availableData, cities };

      // Find Gent
      let city = cities.find(c => c.name === 'Gent');
      if (!city) city = cities[0];

      if (!city) throw new Error('No cities available');

      // Set the default location
      currentLocation = { continent, country, city };

      // Save it
      await locationService.storeLocation(continent, country, city);
    } catch (err) {
      console.error('Failed to initialize default location:', err);
      errorMessage = 'Failed to load default location';
    }
  }

  // Export functions for child components to use
  export async function selectContinent(continent) {
    try {
      currentLocation = { continent, country: null, city: null };
      availableData = { ...availableData, countries: [], cities: [] };

      if (!continent) return;

      const countries = await locationService.getCountries(continent.id);
      availableData = { ...availableData, countries };
    } catch (err) {
      console.error('Failed to load countries:', err);
      errorMessage = 'Failed to load countries';
    }
  }

  export async function selectCountry(country) {
    try {
      currentLocation = { ...currentLocation, country, city: null };
      availableData = { ...availableData, cities: [] };

      if (!country) return;

      const cities = await locationService.getCities(country.id);
      availableData = { ...availableData, cities };
    } catch (err) {
      console.error('Failed to load cities:', err);
      errorMessage = 'Failed to load cities';
    }
  }

  export async function selectCity(city) {
    try {
      currentLocation = { ...currentLocation, city };

      if (!city) return;

      // Save the complete location
      if (currentLocation.continent && currentLocation.country) {
        await locationService.storeLocation(
          currentLocation.continent,
          currentLocation.country,
          city
        );
      }
    } catch (err) {
      console.error('Failed to save city selection:', err);
      errorMessage = 'Failed to save location';
    }
  }

  onMount(async () => {
    await initializeApp();
  });
</script>

<main class="app">
  <TavoosLogo />

  {#if loading}
    <LoadingSpinner message="Loading location data..." />
  {:else if errorMessage}
    <ErrorMessage error={errorMessage} />
  {:else}
    <div class="content">
      <LocationSelector
        {currentLocation}
        {availableData}
        {selectContinent}
        {selectCountry}
        {selectCity}
      />

      {#if hasLocation}
        <QiblaCompass city={currentLocation.city} />
        <div class="location-display" class:highlight={hasLocation}>
          {locationText}
        </div>
      {:else}
        <div class="no-location">
          Please select your location to see Qibla direction
        </div>
      {/if}
    </div>
  {/if}
</main>

<style>
  .app {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    min-height: 100%;
    padding: 0.5rem;
  }

  .content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    width: 100%;
  }

  .location-display {
    text-align: center;
    font-weight: bold;
    font-size: 0.9rem;
    color: var(--text-secondary);
    transition: all 0.3s ease;
    padding: 0.5rem;
    border-radius: 0.5rem;
  }

  .location-display.highlight {
    color: var(--text-primary);
    background: var(--bg-overlay);
  }

  .no-location {
    text-align: center;
    color: var(--text-secondary);
    font-style: italic;
    padding: 2rem 1rem;
  }
</style>
