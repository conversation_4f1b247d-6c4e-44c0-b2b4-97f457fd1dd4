<script>
  import { onMount } from 'svelte';
  import LocationSelector from '../lib/components/LocationSelector.svelte';
  import QiblaCompass from '../lib/components/QiblaCompass.svelte';
  import TavoosLogo from '../lib/components/TavoosLogo.svelte';
  import { locationStore } from '../lib/stores/locationStore.js';
  import { locationService } from '../lib/services/locationService.js';

  let isLoading = true;
  let currentLocation = null;

  // Subscribe to location changes
  locationStore.subscribe(location => {
    currentLocation = location;
  });

  onMount(async () => {
    try {
      // Initialize location data
      await locationService.initializeLocationData();
      isLoading = false;
    } catch (error) {
      console.error('Error initializing popup:', error);
      isLoading = false;
    }
  });
</script>

<main>
  <TavoosLogo />
  
  {#if isLoading}
    <div class="loading">Loading...</div>
  {:else}
    <LocationSelector />
    
    {#if currentLocation?.city}
      <QiblaCompass city={currentLocation.city} />
      <div class="city-info">
        {currentLocation.city.name}, {currentLocation.country?.name || 'Unknown'}
      </div>
    {/if}
  {/if}
</main>

<style>
  main {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }

  .loading {
    text-align: center;
    padding: 2rem;
    color: var(--text-secondary);
  }

  .city-info {
    text-align: center;
    font-weight: bold;
    margin-top: 0.5rem;
  }
</style>
