<script>
  import { onMount } from 'svelte';
  import LocationSelector from '../lib/components/LocationSelector.svelte';
  import QiblaCompass from '../lib/components/QiblaCompass.svelte';
  import TavoosLogo from '../lib/components/TavoosLogo.svelte';
  import LoadingSpinner from '../lib/components/LoadingSpinner.svelte';
  import ErrorMessage from '../lib/components/ErrorMessage.svelte';
  import { location, isLoading, error } from '../lib/stores/appStore.js';
  import { initializeApp } from '../lib/services/appService.js';

  // Reactive statements - this is real Svelte reactivity!
  $: hasLocation = $location.city && $location.country && $location.continent;
  $: locationText = hasLocation
    ? `${$location.city.name}, ${$location.country.name}`
    : 'No location selected';

  onMount(async () => {
    await initializeApp();
  });
</script>

<main class="app">
  <TavoosLogo />

  {#if $isLoading}
    <LoadingSpinner message="Loading location data..." />
  {:else if $error}
    <ErrorMessage {error} />
  {:else}
    <div class="content">
      <LocationSelector />

      {#if hasLocation}
        <QiblaCompass city={$location.city} />
        <div class="location-display" class:highlight={hasLocation}>
          {locationText}
        </div>
      {:else}
        <div class="no-location">
          Please select your location to see Qibla direction
        </div>
      {/if}
    </div>
  {/if}
</main>

<style>
  .app {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    min-height: 100%;
    padding: 0.5rem;
  }

  .content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    width: 100%;
  }

  .location-display {
    text-align: center;
    font-weight: bold;
    font-size: 0.9rem;
    color: var(--text-secondary);
    transition: all 0.3s ease;
    padding: 0.5rem;
    border-radius: 0.5rem;
  }

  .location-display.highlight {
    color: var(--text-primary);
    background: var(--bg-overlay);
  }

  .no-location {
    text-align: center;
    color: var(--text-secondary);
    font-style: italic;
    padding: 2rem 1rem;
  }
</style>
