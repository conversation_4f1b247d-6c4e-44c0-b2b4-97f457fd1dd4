<script>
  import { onMount } from 'svelte';
  import Header from '../lib/components/Header.svelte';
  import PrayerTimes from '../lib/components/PrayerTimes.svelte';
  import QiblaCompass from '../lib/components/QiblaCompass.svelte';
  import QuranVerse from '../lib/components/QuranVerse.svelte';
  import LocationSelector from '../lib/components/LocationSelector.svelte';
  import Footer from '../lib/components/Footer.svelte';

  // Services
  import { locationService } from '../lib/services/locationService.js';
  import { prayerService } from '../lib/services/prayerService.js';
  import { quranService } from '../lib/services/quranService.js';

  // State
  let isLoading = true;
  let currentLocation = null;
  let prayerTimes = null;
  let currentVerse = null;
  let error = null;

  // Initialize the app
  onMount(async () => {
    try {
      // Initialize location service
      await locationService.initialize();

      // Get stored location or set default
      currentLocation = await locationService.getStoredLocation();

      if (!currentLocation.city) {
        // Set default to Gent, Belgium
        await setDefaultLocation();
      }

      // Load prayer times and Quran verse
      await Promise.all([
        loadPrayerTimes(),
        loadQuranVerse()
      ]);

    } catch (err) {
      console.error('Failed to initialize app:', err);
      error = 'Failed to load Islamic content. Please refresh the page.';
    } finally {
      isLoading = false;
    }
  });

  async function setDefaultLocation() {
    try {
      const continents = await locationService.getContinents();
      const europe = continents.find(c => c.code === 'EU') || continents[0];

      const countries = await locationService.getCountries(europe.id);
      const belgium = countries.find(c => c.code === 'BE') || countries[0];

      const cities = await locationService.getCities(belgium.id);
      const gent = cities.find(c => c.name === 'Gent') || cities[0];

      currentLocation = {
        continent: europe,
        country: belgium,
        city: gent
      };

      await locationService.storeLocation(europe, belgium, gent);
    } catch (err) {
      console.error('Failed to set default location:', err);
    }
  }

  async function loadPrayerTimes() {
    if (!currentLocation?.city) return;

    try {
      prayerTimes = await prayerService.getTodaysPrayerTimes(
        currentLocation.city.latitude,
        currentLocation.city.longitude
      );
    } catch (err) {
      console.error('Failed to load prayer times:', err);
    }
  }

  async function loadQuranVerse() {
    try {
      currentVerse = await quranService.getRandomVerse();
    } catch (err) {
      console.error('Failed to load Quran verse:', err);
    }
  }

  // Handle location change
  async function handleLocationChange(newLocation) {
    currentLocation = newLocation;
    await loadPrayerTimes();
  }

  // Handle verse refresh
  async function handleVerseRefresh() {
    await loadQuranVerse();
  }
</script>

<main class="app">
  {#if isLoading}
    <div class="loading-container">
      <div class="spinner"></div>
      <p>Loading your spiritual moments...</p>
    </div>
  {:else if error}
    <div class="error-container">
      <h2>⚠️ Something went wrong</h2>
      <p>{error}</p>
      <button on:click={() => window.location.reload()}>
        Try Again
      </button>
    </div>
  {:else}
    <Header />

    <div class="content">
      <div class="main-grid">
        <!-- Left Column: Prayer Times & Location -->
        <div class="left-column">
          <LocationSelector
            {currentLocation}
            onLocationChange={handleLocationChange}
          />

          {#if prayerTimes}
            <PrayerTimes
              {prayerTimes}
              cityName={currentLocation?.city?.name}
            />
          {/if}
        </div>

        <!-- Center Column: Qibla Compass -->
        <div class="center-column">
          {#if currentLocation?.city}
            <QiblaCompass
              city={currentLocation.city}
              cityName={currentLocation.city.name}
              countryName={currentLocation.country?.name}
            />
          {/if}
        </div>

        <!-- Right Column: Quran Verse -->
        <div class="right-column">
          {#if currentVerse}
            <QuranVerse
              verse={currentVerse}
              onRefresh={handleVerseRefresh}
            />
          {/if}
        </div>
      </div>
    </div>

    <Footer />
  {/if}
</main>

<style>
  .app {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    gap: 1rem;
  }

  .error-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    gap: 1rem;
    text-align: center;
    padding: 2rem;
  }

  .error-container button {
    background: var(--button-primary-bg);
    color: var(--button-primary-text);
    border: 1px solid var(--button-secondary-border);
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.2s ease;
  }

  .error-container button:hover {
    background: white;
    transform: translateY(-1px);
  }

  .content {
    flex: 1;
    padding: 2rem;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
  }

  .main-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 2rem;
    min-height: 70vh;
  }

  .left-column,
  .center-column,
  .right-column {
    display: flex;
    flex-direction: column;
    gap: 2rem;
  }

  .center-column {
    align-items: center;
    justify-content: center;
  }

  /* Responsive Design */
  @media (max-width: 1200px) {
    .main-grid {
      grid-template-columns: 1fr 1fr;
      grid-template-rows: auto auto;
    }

    .center-column {
      grid-column: 1 / -1;
      order: -1;
    }
  }

  @media (max-width: 768px) {
    .content {
      padding: 1rem;
    }

    .main-grid {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }

    .center-column {
      order: 0;
    }
  }

  .spinner {
    width: 50px;
    height: 50px;
    border: 4px solid var(--bg-overlay);
    border-top: 4px solid var(--tavoos-light);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
</style>
