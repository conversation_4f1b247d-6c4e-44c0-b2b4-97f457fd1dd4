/* Global styles for Tavoos Svelte New Tab */

:root {
  /* Tavoos Brand Colors */
  --tavoos-primary: rgb(0, 121, 107);
  --tavoos-dark: rgb(0, 90, 80);
  --tavoos-darker: rgb(0, 70, 60);
  --tavoos-light: rgb(0, 200, 180);

  /* UI Colors */
  --text-primary: white;
  --text-secondary: rgba(255, 255, 255, 0.8);
  --bg-overlay: rgba(255, 255, 255, 0.1);
  --border-light: rgba(255, 255, 255, 0.2);
  --border-medium: rgba(255, 255, 255, 0.3);
  --highlight-bg: rgba(255, 255, 255, 0.25);

  /* Button Colors */
  --button-primary-bg: #ff8c00;
  --button-primary-text: black;
  --button-secondary-bg: transparent;
  --button-secondary-text: white;
  --button-secondary-border: black;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background: linear-gradient(135deg, var(--tavoos-primary) 0%, var(--tavoos-dark) 100%);
  color: var(--text-primary);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  min-height: 100vh;
  overflow-x: hidden;
}

#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Arabic Font for Quran */
@font-face {
  font-family: 'me_quran';
  src: url('./fonts/me_quran.ttf') format('truetype');
}

.arabic-text {
  font-family: 'me_quran', Arial, sans-serif;
  direction: rtl;
  text-align: right;
  color: var(--text-primary);
}

/* Common UI Elements */
.card {
  background: var(--bg-overlay);
  border: 1px solid var(--border-medium);
  border-radius: 1rem;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.card:hover {
  background: var(--highlight-bg);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.btn-primary {
  background: var(--button-primary-bg);
  color: var(--button-primary-text);
  border: 1px solid var(--button-secondary-border);
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.btn-primary:hover {
  background: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.btn-secondary {
  background: var(--button-secondary-bg);
  color: var(--button-secondary-text);
  border: 1px solid var(--button-secondary-border);
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.btn-secondary:hover {
  background: var(--bg-overlay);
  transform: translateY(-1px);
}

/* Form Elements */
select, input {
  background: var(--bg-overlay);
  color: var(--text-primary);
  border: 1px solid var(--border-medium);
  border-radius: 0.5rem;
  padding: 0.75rem;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

select:focus, input:focus {
  outline: none;
  border-color: var(--tavoos-light);
  box-shadow: 0 0 0 2px rgba(0, 200, 180, 0.2);
}

select option {
  background: var(--tavoos-primary);
  color: var(--text-primary);
}

/* Links */
a {
  color: var(--text-primary);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: var(--tavoos-light);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.fade-in {
  animation: fadeIn 0.6s ease-out;
}

.slide-in {
  animation: slideIn 0.6s ease-out;
}

.pulse {
  animation: pulse 2s ease-in-out infinite;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-overlay);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--tavoos-light);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--tavoos-primary);
}

/* Responsive Typography */
@media (max-width: 768px) {
  body {
    font-size: 14px;
  }

  .card {
    padding: 1rem;
    border-radius: 0.75rem;
  }

  .btn-primary,
  .btn-secondary {
    padding: 0.6rem 1.2rem;
    font-size: 0.85rem;
  }
}
