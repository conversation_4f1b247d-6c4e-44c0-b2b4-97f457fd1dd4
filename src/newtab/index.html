<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Tavoos - Spiritual Moments</title>
  <style>
    :root {
      /* Tavoos Brand Colors */
      --tavoos-primary: rgb(0, 121, 107);
      --tavoos-dark: rgb(0, 90, 80);
      --tavoos-darker: rgb(0, 70, 60);
      --tavoos-light: rgb(0, 200, 180);

      /* UI Colors */
      --text-primary: white;
      --text-secondary: rgba(255, 255, 255, 0.8);
      --bg-overlay: rgba(255, 255, 255, 0.1);
      --border-light: rgba(255, 255, 255, 0.2);
      --border-medium: rgba(255, 255, 255, 0.3);
      --highlight-bg: rgba(255, 255, 255, 0.25);

      /* Button Colors */
      --button-primary-bg: #ff8c00;
      --button-primary-text: black;
      --button-secondary-bg: transparent;
      --button-secondary-text: white;
      --button-secondary-border: black;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      background: linear-gradient(135deg, var(--tavoos-primary) 0%, var(--tavoos-dark) 100%);
      color: var(--text-primary);
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      min-height: 100vh;
      overflow-x: hidden;
    }

    /* Arabic Font for Quran */
    @font-face {
      font-family: 'me_quran';
      src: url('./fonts/me_quran.ttf') format('truetype');
    }

    .arabic-text {
      font-family: 'me_quran', Arial, sans-serif;
      direction: rtl;
      text-align: right;
    }

    a {
      color: var(--text-primary);
      text-decoration: none;
    }

    a:hover {
      color: var(--tavoos-light);
    }

    /* Loading animation */
    .loading {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      font-size: 1.2rem;
    }

    .spinner {
      width: 40px;
      height: 40px;
      border: 3px solid var(--bg-overlay);
      border-top: 3px solid var(--tavoos-light);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-right: 1rem;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
  <script type="module" crossorigin src="./main.js"></script>
</head>
<body>
  <div id="app">
    <div class="loading">
      <div class="spinner"></div>
      Loading Tavoos...
    </div>
  </div>
</body>
</html>
