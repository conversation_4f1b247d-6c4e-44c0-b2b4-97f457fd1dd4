// Tavoos New Tab Page JavaScript

// DOM Elements
const currentDateElement = document.getElementById('current-date');
const currentTimeElement = document.getElementById('current-time');
const continentsSelect = document.getElementById('continents');
const countriesSelect = document.getElementById('countries');
const citiesSelect = document.getElementById('cities');
const locationDisplay = document.getElementById('location-display');
const compass = document.getElementById('compass');
const degreesElement = document.getElementById('degrees');
const minutesElement = document.getElementById('minutes');
const arabicVerseElement = document.getElementById('arabic-verse');
const verseTranslationElement = document.getElementById('verse-translation');
const verseReferenceElement = document.getElementById('verse-reference');
const newVerseBtn = document.getElementById('new-verse-btn');
const verseDetailsLink = document.getElementById('verse-details-link');
const recitationAudio = document.getElementById('recitation-audio');

// Notification Settings Elements
const settingsBtn = document.getElementById('settings-btn');
const settingsModal = document.getElementById('settings-modal');
const closeBtn = document.querySelector('.close');
const notificationsEnabled = document.getElementById('notifications-enabled');
// No longer using minutes-before
const prayerFajr = document.getElementById('prayer-fajr');
const prayerDhuhr = document.getElementById('prayer-dhuhr');
const prayerAsr = document.getElementById('prayer-asr');
const prayerMaghrib = document.getElementById('prayer-maghrib');
const prayerIsha = document.getElementById('prayer-isha');
const saveSettingsBtn = document.getElementById('save-settings');

// Prayer time elements
const fajrTimeElement = document.getElementById('fajr-time');
const sunriseTimeElement = document.getElementById('sunrise-time');
const dhuhrTimeElement = document.getElementById('dhuhr-time');
const asrTimeElement = document.getElementById('asr-time');
const maghribTimeElement = document.getElementById('maghrib-time');
const ishaTimeElement = document.getElementById('isha-time');

// Global variables
let continents = [];
let countries = [];
let cities = [];
let currentCity = null;
let currentContinent = null;
let currentCountry = null;

// Initialize the page
async function initializePage() {
  updateDateTime();
  setInterval(updateDateTime, 1000);

  // Hide translation element since we don't have translations
  verseTranslationElement.style.display = 'none';

  // Set up event listeners
  continentsSelect.addEventListener('change', onContinentSelect);
  countriesSelect.addEventListener('change', onCountrySelect);
  citiesSelect.addEventListener('change', onCitySelect);
  newVerseBtn.addEventListener('click', loadNewVerse);

  // Set up notification settings event listeners
  settingsBtn.addEventListener('click', openSettingsModal);
  closeBtn.addEventListener('click', closeSettingsModal);
  saveSettingsBtn.addEventListener('click', saveNotificationSettings);

  // Close modal when clicking outside of it
  window.addEventListener('click', (event) => {
    if (event.target === settingsModal) {
      closeSettingsModal();
    }
  });

  // Load notification settings
  loadNotificationSettings();

  // Initialize location data and load stored location
  await initializeLocationData();

  // Load a random verse for each new tab
  await loadNewVerse();

  // Preload verses for faster access later
  quranService.preloadVerses();
}

// Update date and time display
function updateDateTime() {
  const now = new Date();

  // Format date: Monday, January 1, 2023
  const dateOptions = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
  currentDateElement.textContent = now.toLocaleDateString(undefined, dateOptions);

  // Format time: 12:34:56 PM
  const timeOptions = { hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: true };
  currentTimeElement.textContent = now.toLocaleTimeString(undefined, timeOptions);
}

// Initialize location data
async function initializeLocationData() {
  try {
    // Load continents
    continents = await locationService.getContinents();
    populateContinentsDropdown();

    // Try to load stored location first
    const storedLocation = await locationService.getStoredLocation();

    if (storedLocation.continent && storedLocation.country && storedLocation.city) {
      // Use stored location
      currentContinent = storedLocation.continent;
      currentCountry = storedLocation.country;
      currentCity = storedLocation.city;

      // Populate dropdowns with stored data
      await populateCountriesDropdown(currentContinent.id);
      await populateCitiesDropdown(currentCountry.id);

      // Set selected values
      continentsSelect.value = currentContinent.id;
      countriesSelect.value = currentCountry.id;
      citiesSelect.value = currentCity.id;

      // Update UI
      await updateSelectedCity(currentCity);
    } else {
      // Initialize with default location
      const defaultLocation = await locationService.initializeDefaultLocation();
      if (defaultLocation) {
        currentContinent = defaultLocation.continent;
        currentCountry = defaultLocation.country;
        currentCity = defaultLocation.city;

        // Populate dropdowns
        await populateCountriesDropdown(currentContinent.id);
        await populateCitiesDropdown(currentCountry.id);

        // Set selected values
        continentsSelect.value = currentContinent.id;
        countriesSelect.value = currentCountry.id;
        citiesSelect.value = currentCity.id;

        // Update UI
        await updateSelectedCity(currentCity);
      }
    }
  } catch (error) {
    console.error('Error initializing location data:', error);
  }
}

// Populate continents dropdown
function populateContinentsDropdown() {
  continentsSelect.innerHTML = '<option value="">Select Continent</option>';

  continents.forEach(continent => {
    const option = document.createElement('option');
    option.value = continent.id;
    option.textContent = continent.name;
    continentsSelect.appendChild(option);
  });
}

// Populate countries dropdown
async function populateCountriesDropdown(continentId) {
  countriesSelect.innerHTML = '<option value="">Select Country</option>';
  citiesSelect.innerHTML = '<option value="">Select City</option>';

  if (!continentId) return;

  try {
    countries = await locationService.getCountries(continentId);

    countries.forEach(country => {
      const option = document.createElement('option');
      option.value = country.id;
      option.textContent = country.name;
      countriesSelect.appendChild(option);
    });
  } catch (error) {
    console.error('Error loading countries:', error);
  }
}

// Populate cities dropdown
async function populateCitiesDropdown(countryId) {
  citiesSelect.innerHTML = '<option value="">Select City</option>';

  if (!countryId) return;

  try {
    cities = await locationService.getCities(countryId);

    cities.forEach(city => {
      const option = document.createElement('option');
      option.value = city.id;
      option.textContent = city.name;
      citiesSelect.appendChild(option);
    });
  } catch (error) {
    console.error('Error loading cities:', error);
  }
}

// Handle continent selection
async function onContinentSelect(event) {
  const continentId = event.target.value;
  if (!continentId) {
    countriesSelect.innerHTML = '<option value="">Select Country</option>';
    citiesSelect.innerHTML = '<option value="">Select City</option>';
    return;
  }

  currentContinent = continents.find(c => c.id == continentId);
  currentCountry = null;
  currentCity = null;

  await populateCountriesDropdown(continentId);
}

// Handle country selection
async function onCountrySelect(event) {
  const countryId = event.target.value;
  if (!countryId) {
    citiesSelect.innerHTML = '<option value="">Select City</option>';
    return;
  }

  currentCountry = countries.find(c => c.id == countryId);
  currentCity = null;

  await populateCitiesDropdown(countryId);
}

// Handle city selection
async function onCitySelect(event) {
  const cityId = event.target.value;
  if (!cityId) return;

  const selectedCity = cities.find(city => city.id == cityId);
  if (selectedCity) {
    currentCity = selectedCity;

    // Store the complete location selection
    await locationService.storeLocation(currentContinent, currentCountry, selectedCity);

    await updateSelectedCity(selectedCity);
  }
}

// Update UI with selected city information
async function updateSelectedCity(city) {
  // Display city name with country
  const countryName = currentCountry ? currentCountry.name : 'Unknown';
  locationDisplay.textContent = `${city.name}, ${countryName}`;

  // Update Qibla direction
  updateQiblaDirection(city);

  // Update prayer times
  await updatePrayerTimes(city);
}

// Calculate and display Qibla direction
function updateQiblaDirection(city) {
  const { latitude, longitude } = city;
  const brng = calculateQiblaDirection(latitude, longitude);

  // Set up the map background using OpenStreetMap
  const zoom = 12; // Zoom level (higher = more detailed)

  // Create an OpenStreetMap URL (no API key required)
  const mapUrl = `https://tile.openstreetmap.org/${zoom}/${Math.floor((longitude + 180) / 360 * Math.pow(2, zoom))}/${Math.floor((1 - Math.log(Math.tan(latitude * Math.PI / 180) + 1 / Math.cos(latitude * Math.PI / 180)) / Math.PI) / 2 * Math.pow(2, zoom))}.png`;

  // Set the map as background for the entire qibla direction section
  const qiblaSection = document.querySelector('.qibla-direction');
  qiblaSection.style.setProperty('--map-url', `url('${mapUrl}')`);

  // Apply the map background to the ::before pseudo-element
  const style = document.createElement('style');
  style.textContent = `
    .qibla-direction::before {
      background-image: var(--map-url);
      background-size: cover;
      background-position: center;
      filter: grayscale(100%) contrast(1.4) brightness(1.1) invert(1) opacity(50%);
    }
  `;

  // Remove any previous style element
  const oldStyle = document.getElementById('map-style');
  if (oldStyle) {
    oldStyle.remove();
  }

  // Add the new style element
  style.id = 'map-style';
  document.head.appendChild(style);

  // Clear the compass
  compass.innerHTML = '';

  // Set the compass styling
  compass.style.backgroundImage = `url('../qibla/images/compass_rose.png')`;
  compass.style.backgroundSize = '80%';
  compass.style.backgroundPosition = 'center';
  compass.style.backgroundRepeat = 'no-repeat';
  compass.style.backgroundColor = 'transparent';
  // No border or border radius needed
  compass.style.position = 'relative';
  compass.style.zIndex = '1';
  compass.style.filter = 'invert(1)';
  compass.style.transform = 'scale(1.85)';

  // Create and position the compass hand
  const hand = document.createElement('div');
  hand.classList.add('hand');
  hand.style.transform = `translate(125px, 125px) translate(0, -2px) rotate(${brng + 90 - 11.5}deg)`;
  hand.style.zIndex = '20';

  compass.appendChild(hand);

  // Display the bearing
  const degrees = Math.floor(brng);
  const minutes = Math.floor(60 * (brng - degrees));

  degreesElement.textContent = degrees;
  minutesElement.textContent = minutes;
}

// Calculate Qibla direction
function calculateQiblaDirection(latitude, longitude) {
  const toRadians = deg => deg * Math.PI / 180;
  const toDegrees = rad => rad * 180 / Math.PI;

  const startLat = toRadians(latitude);
  const startLng = toRadians(longitude);

  // Coordinates of Mecca
  const destLat = toRadians(21.3891);
  const destLng = toRadians(39.8579);

  const y = Math.sin(destLng - startLng) * Math.cos(destLat);
  const x = Math.cos(startLat) * Math.sin(destLat) - Math.sin(startLat) * Math.cos(destLat) * Math.cos(destLng - startLng);
  const brng = toDegrees(Math.atan2(y, x));

  return brng + 180;
}

// Update prayer times for the selected city
async function updatePrayerTimes(city) {
  try {
    const prayerTimes = await prayerTimesService.getPrayerTimesByCity(city.name, city.country);

    if (prayerTimes) {
      // Update the UI with prayer times
      fajrTimeElement.textContent = prayerTimesService.formatTime(prayerTimes.Fajr);
      sunriseTimeElement.textContent = prayerTimesService.formatTime(prayerTimes.Sunrise);
      dhuhrTimeElement.textContent = prayerTimesService.formatTime(prayerTimes.Dhuhr);
      asrTimeElement.textContent = prayerTimesService.formatTime(prayerTimes.Asr);
      maghribTimeElement.textContent = prayerTimesService.formatTime(prayerTimes.Maghrib);
      ishaTimeElement.textContent = prayerTimesService.formatTime(prayerTimes.Isha);

      // Highlight the next prayer
      highlightNextPrayer(prayerTimes);
    }
  } catch (error) {
    console.error('Error updating prayer times:', error);
  }
}

// Highlight the next upcoming prayer
function highlightNextPrayer(prayerTimes) {
  const now = new Date();
  const currentHour = now.getHours();
  const currentMinute = now.getMinutes();
  const currentTime = currentHour * 60 + currentMinute;

  const prayers = [
    { name: 'Dawn', element: fajrTimeElement, time: prayerTimes.Fajr },
    { name: 'Sunrise', element: sunriseTimeElement, time: prayerTimes.Sunrise },
    { name: 'Noon', element: dhuhrTimeElement, time: prayerTimes.Dhuhr },
    { name: 'Afternoon', element: asrTimeElement, time: prayerTimes.Asr },
    { name: 'Dusk', element: maghribTimeElement, time: prayerTimes.Maghrib },
    { name: 'Dinner', element: ishaTimeElement, time: prayerTimes.Isha }
  ];

  // Reset all highlights
  prayers.forEach(prayer => {
    prayer.element.parentElement.classList.remove('active-prayer');
  });

  // Convert prayer times to minutes since midnight
  prayers.forEach(prayer => {
    const [hours, minutes] = prayer.time?.split(':') ?? [0, 0];
    prayer.minutesSinceMidnight = parseInt(hours) * 60 + parseInt(minutes);
  });

  // Find the next prayer
  const nextPrayer = prayers.find(prayer => prayer.minutesSinceMidnight > currentTime);

  // If found, highlight it
  if (nextPrayer) {
    nextPrayer.element.parentElement.classList.add('active-prayer');
  } else {
    // If no next prayer today, highlight Fajr for tomorrow
    prayers[0].element.parentElement.classList.add('active-prayer');
  }
}

// This function is kept for backward compatibility
// It now just loads a random verse
async function loadVerseOfTheDay() {
  await loadNewVerse();
}

// Load a new verse when button is clicked
async function loadNewVerse() {
  try {
    const verse = await quranService.getNextVerse();
    displayVerse(verse);
  } catch (error) {
    console.error('Error loading new verse:', error);
  }
}

// Display a verse on the page
function displayVerse(verse) {
  // Set the Arabic text with proper font
  arabicVerseElement.textContent = verse.arabic;
  arabicVerseElement.style.fontFamily = "'Me Quran', 'Traditional Arabic', 'Scheherazade', serif";

  // Hide the translation element since we don't have translations
  verseTranslationElement.style.display = 'none';

  // Set the reference
  verseReferenceElement.textContent = `${verse.surahEnglishName} (${verse.surahNumber}:${verse.verseNumber})`;

  // Set the details link URL
  verseDetailsLink.href = `https://tavoos.eu/quran/verse/${verse.surahNumber}/${verse.verseNumber}`;

  // Set up the recitation audio for the current verse
  console.log('Setting audio source:', verse.recitationUrl);
  recitationAudio.src = verse.recitationUrl;

  // Stop any currently playing audio
  recitationAudio.pause();
  recitationAudio.currentTime = 0;

  // Add bismillah before the verse for aesthetic purposes (except for Surah 9)
  if (verse.surahNumber !== 9 && verse.verseNumber === 1) {
    const bismillah = document.createElement('div');
    bismillah.className = 'bismillah';
    bismillah.textContent = 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ';
    bismillah.style.fontFamily = "'Me Quran', 'Traditional Arabic', 'Scheherazade', serif";
    bismillah.style.marginBottom = '15px';
    bismillah.style.fontSize = '24px';

    // Insert bismillah before the verse
    arabicVerseElement.parentNode.insertBefore(bismillah, arabicVerseElement);
  } else {
    // Remove bismillah if it exists
    const existingBismillah = document.querySelector('.bismillah');
    if (existingBismillah) {
      existingBismillah.remove();
    }
  }
}



// Notification Settings Functions

// Open the settings modal
function openSettingsModal() {
  settingsModal.style.display = 'block';
}

// Close the settings modal
function closeSettingsModal() {
  settingsModal.style.display = 'none';
}

// Load notification settings from storage
function loadNotificationSettings() {
  chrome.storage.sync.get(['notificationSettings'], (result) => {
    if (result.notificationSettings) {
      const settings = result.notificationSettings;

      // Update UI with stored settings
      notificationsEnabled.checked = settings.enabled;

      // Update prayer checkboxes
      prayerFajr.checked = settings.prayers.Fajr;
      prayerDhuhr.checked = settings.prayers.Dhuhr;
      prayerAsr.checked = settings.prayers.Asr;
      prayerMaghrib.checked = settings.prayers.Maghrib;
      prayerIsha.checked = settings.prayers.Isha;
    }
  });
}

// Save notification settings to storage
function saveNotificationSettings() {
  const settings = {
    enabled: notificationsEnabled.checked,
    prayers: {
      Fajr: prayerFajr.checked,     // Dawn
      Sunrise: false,               // Not typically considered a prayer time
      Dhuhr: prayerDhuhr.checked,   // Noon
      Asr: prayerAsr.checked,       // Afternoon
      Maghrib: prayerMaghrib.checked, // Dusk
      Isha: prayerIsha.checked      // Dinner
    }
  };

  chrome.storage.sync.set({ notificationSettings: settings }, () => {
    console.log('Notification settings saved');

    // Show a temporary success message
    const saveBtn = document.getElementById('save-settings');
    const originalText = saveBtn.textContent;
    saveBtn.textContent = 'Settings Saved!';
    saveBtn.style.backgroundColor = 'var(--tavoos-dark)';

    setTimeout(() => {
      saveBtn.textContent = originalText;
      saveBtn.style.backgroundColor = 'var(--tavoos-light)';
      closeSettingsModal();
    }, 1500);
  });
}

// Initialize the page when DOM is loaded
document.addEventListener('DOMContentLoaded', initializePage);
