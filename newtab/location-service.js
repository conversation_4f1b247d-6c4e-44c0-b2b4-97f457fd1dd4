// Location Service for Tavoos Chrome Extension
// Handles remote API calls for continents, countries, and cities data

class LocationService {
  constructor() {
    this.baseUrl = 'https://behnegar.app/api/v1/tavoos/pray-times-v2';
    this.storageKeys = {
      continents: 'tavoos_continents',
      countries: 'tavoos_countries',
      cities: 'tavoos_cities',
      selectedContinent: 'tavoos_selected_continent',
      selectedCountry: 'tavoos_selected_country',
      selectedCity: 'tavoos_selected_city'
    };
    
    // Default fallback values
    this.defaults = {
      continent: { code: 'EU', name: 'Europe' },
      country: { 
        europe: { code: 'BE', name: 'Belgium' },
        asia: { code: 'IR', name: 'Iran' }
      },
      city: { name: 'Gent' }
    };
  }

  // Fetch continents from API or cache
  async getContinents() {
    try {
      // Try to get from cache first
      const cached = await this.getFromStorage(this.storageKeys.continents);
      if (cached && cached.length > 0) {
        return cached;
      }

      // Fetch from API
      const response = await fetch(`${this.baseUrl}/continents`);
      if (!response.ok) {
        throw new Error(`Failed to fetch continents: ${response.status}`);
      }

      const continents = await response.json();
      
      // Cache the result
      await this.saveToStorage(this.storageKeys.continents, continents);
      
      return continents;
    } catch (error) {
      console.error('Error fetching continents:', error);
      // Return empty array if all fails
      return [];
    }
  }

  // Fetch countries for a continent from API or cache
  async getCountries(continentId) {
    try {
      // Create cache key for this continent
      const cacheKey = `${this.storageKeys.countries}_${continentId}`;
      
      // Try to get from cache first
      const cached = await this.getFromStorage(cacheKey);
      if (cached && cached.length > 0) {
        return cached;
      }

      // Fetch from API
      const response = await fetch(`${this.baseUrl}/continents/${continentId}/countries`);
      if (!response.ok) {
        throw new Error(`Failed to fetch countries: ${response.status}`);
      }

      const countries = await response.json();
      
      // Cache the result
      await this.saveToStorage(cacheKey, countries);
      
      return countries;
    } catch (error) {
      console.error('Error fetching countries:', error);
      return [];
    }
  }

  // Fetch cities for a country from API or cache
  async getCities(countryId) {
    try {
      // Create cache key for this country
      const cacheKey = `${this.storageKeys.cities}_${countryId}`;
      
      // Try to get from cache first
      const cached = await this.getFromStorage(cacheKey);
      if (cached && cached.length > 0) {
        return cached;
      }

      // Fetch from API
      const response = await fetch(`${this.baseUrl}/countries/${countryId}/cities`);
      if (!response.ok) {
        throw new Error(`Failed to fetch cities: ${response.status}`);
      }

      const cities = await response.json();
      
      // Cache the result
      await this.saveToStorage(cacheKey, cities);
      
      return cities;
    } catch (error) {
      console.error('Error fetching cities:', error);
      return [];
    }
  }

  // Clear cached countries when continent changes
  async clearCountriesCache(continentId) {
    const cacheKey = `${this.storageKeys.countries}_${continentId}`;
    await this.removeFromStorage(cacheKey);
  }

  // Clear cached cities when country changes
  async clearCitiesCache(countryId) {
    const cacheKey = `${this.storageKeys.cities}_${countryId}`;
    await this.removeFromStorage(cacheKey);
  }

  // Get stored location selection
  async getStoredLocation() {
    try {
      const continent = await this.getFromStorage(this.storageKeys.selectedContinent);
      const country = await this.getFromStorage(this.storageKeys.selectedCountry);
      const city = await this.getFromStorage(this.storageKeys.selectedCity);
      
      return { continent, country, city };
    } catch (error) {
      console.error('Error getting stored location:', error);
      return { continent: null, country: null, city: null };
    }
  }

  // Store location selection
  async storeLocation(continent, country, city) {
    try {
      await this.saveToStorage(this.storageKeys.selectedContinent, continent);
      await this.saveToStorage(this.storageKeys.selectedCountry, country);
      await this.saveToStorage(this.storageKeys.selectedCity, city);
    } catch (error) {
      console.error('Error storing location:', error);
    }
  }

  // Initialize with default location (EU -> BE -> Gent)
  async initializeDefaultLocation() {
    try {
      // Get continents
      const continents = await this.getContinents();
      if (continents.length === 0) {
        throw new Error('No continents available');
      }

      // Find Europe continent
      let selectedContinent = continents.find(c => c.code === this.defaults.continent.code);
      if (!selectedContinent) {
        selectedContinent = continents[0]; // Fallback to first continent
      }

      // Get countries for the selected continent
      const countries = await this.getCountries(selectedContinent.id);
      if (countries.length === 0) {
        throw new Error('No countries available');
      }

      // Find default country based on continent
      let selectedCountry;
      if (selectedContinent.code === 'EU') {
        selectedCountry = countries.find(c => c.code === this.defaults.country.europe.code);
      } else if (selectedContinent.code === 'AS') {
        selectedCountry = countries.find(c => c.code === this.defaults.country.asia.code);
      }
      
      if (!selectedCountry) {
        selectedCountry = countries[0]; // Fallback to first country
      }

      // Get cities for the selected country
      const cities = await this.getCities(selectedCountry.id);
      if (cities.length === 0) {
        throw new Error('No cities available');
      }

      // Find Gent or fallback to first city
      let selectedCity = cities.find(c => c.name === this.defaults.city.name);
      if (!selectedCity) {
        selectedCity = cities[0]; // Fallback to first city
      }

      // Store the default selection
      await this.storeLocation(selectedContinent, selectedCountry, selectedCity);

      return { continent: selectedContinent, country: selectedCountry, city: selectedCity };
    } catch (error) {
      console.error('Error initializing default location:', error);
      return null;
    }
  }

  // Helper methods for Chrome storage
  async getFromStorage(key) {
    return new Promise((resolve) => {
      chrome.storage.sync.get([key], (result) => {
        resolve(result[key] || null);
      });
    });
  }

  async saveToStorage(key, value) {
    return new Promise((resolve) => {
      chrome.storage.sync.set({ [key]: value }, () => {
        resolve();
      });
    });
  }

  async removeFromStorage(key) {
    return new Promise((resolve) => {
      chrome.storage.sync.remove([key], () => {
        resolve();
      });
    });
  }
}

// Export the service
const locationService = new LocationService();
