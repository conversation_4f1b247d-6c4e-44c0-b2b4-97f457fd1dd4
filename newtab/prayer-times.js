// Prayer Times Service

class PrayerTimesService {
  constructor() {
    this.apiUrl = 'https://behnegar.app/api/v1/tavoos/pray-times-v2/coordinates';
  }

  // Fetch prayer times for a specific city object (with coordinates)
  async getPrayerTimesByCity(cityObject) {
    if (cityObject && cityObject.latitude && cityObject.longitude) {
      return this.getPrayerTimesByCoordinates(cityObject.latitude, cityObject.longitude);
    } else {
      console.error('City object must have latitude and longitude properties');
      return null;
    }
  }

  // Fetch prayer times based on coordinates
  async getPrayerTimesByCoordinates(latitude, longitude) {
    try {
      const url = `${this.apiUrl}/${latitude}/${longitude}/schedule?period=today`;
      const response = await fetch(url);

      if (!response.ok) {
        throw new Error('Failed to fetch prayer times');
      }

      const data = await response.json();

      // Map the Tavoos API response to the format expected by the UI
      return {
        Fajr: data.fajr,
        Sunrise: data.sunrise,
        Dhuhr: data.dhohr,
        Asr: data.aser,
        Maghrib: data.maghreb,
        Isha: data.isha
      };
    } catch (error) {
      console.error('Error fetching prayer times by coordinates:', error);
      return null;
    }
  }

  // Format time from 24-hour to 12-hour format
  formatTime(time24) {
    const [hours, minutes] = time24?.split(':') ?? [0, 0];
    const hour = parseInt(hours, 10);
    const period = hour >= 12 ? 'PM' : 'AM';
    const hour12 = hour % 12 || 12;
    return `${hour12}:${minutes} ${period}`;
  }

  // Get user's current location
  async getCurrentLocation() {
    return new Promise((resolve, reject) => {
      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
          (position) => {
            resolve({
              latitude: position.coords.latitude,
              longitude: position.coords.longitude
            });
          },
          (error) => {
            console.error('Error getting location:', error);
            reject(error);
          }
        );
      } else {
        reject(new Error('Geolocation is not supported by this browser.'));
      }
    });
  }
}

// Export the service
const prayerTimesService = new PrayerTimesService();
